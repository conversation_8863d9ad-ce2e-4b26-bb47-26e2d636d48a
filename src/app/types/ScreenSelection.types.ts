import * as THREE from "three";

export interface ScreenSelectionV2 {
  top_left: THREE.Vector2;
  top_right: THREE.Vector2;
  bottom_left: THREE.Vector2;
  bottom_right: THREE.Vector2;
}

export interface ScreenSelectionV3 {
  top_left: THREE.Vector3 | null;
  top_right: THREE.Vector3 | null;
  bottom_left: THREE.Vector3 | null;
  bottom_right: THREE.Vector3 | null;
  middle: THREE.Vector3 | null;

  top_left_intersection: THREE.Intersection | null,
  top_right_intersection: THREE.Intersection | null,
  bottom_left_intersection: THREE.Intersection | null,
  bottom_right_intersection: THREE.Intersection | null,
  middle_intersection: THREE.Intersection | null,

  hits: number;
}
