import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Child, ElementRef} from '@angular/core';

//Services
import { HelpersService } from "../services/helpers/helpers.service";
import { ModelsService } from "../services/models/models.service";
import { MeasurementsService } from "../services/measurements/measurements.service";

import {AuthService} from "../services/auth/auth.service";
import {SceneService} from "../services/scene/scene.service";
import {UiService} from "../services/ui/ui.service";
import {ActivatedRoute} from "@angular/router";
import {MetadataService} from "../services/metadata/metadata.service";
import {TutorialService} from "../services/tutorial/tutorial.service";
import {CameraService} from "../services/camera/camera.service";
import {GpsService} from "../services/gps/gps.service";
import {SelectionService} from "../services/selection/selection.service";
import {InsideNavigationService} from "../services/inside_navigation/inside-navigation.service";
import {DebugService} from "../services/debug/debug.service";
import {DatasetsService} from "../services/datasets/datasets.service";
import {PreloadService} from "../services/preload/preload.service";
import {PreviewService} from "../services/preview/preview.service";
import {ThermalService} from "../services/thermal/thermal.service";
import {LevelService} from "../services/level/level.service";
import {SurfaceInclanationService} from "../services/SurfaceInclanation/surface-inclanation.service";
import {ScreenSelectionService} from "../services/ScreenSelection/screen-selection.service";
import {CommentsService} from "../services/Comments/comments.service";

@Component({
  selector: 'app-editor',
  templateUrl: './editor.component.html',
  styleUrl: './editor.component.css'
})
export class EditorComponent implements OnInit{

  @ViewChild('canvas', {static: true}) canvasRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('surfaceInclanationCanvas', { static: false }) surfaceInclanationRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('surfaceInclanationSideChartCanvas', { static: false }) surfaceInclanationSideChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('surfaceInclanationBottomChartCanvas', { static: false }) surfaceInclanationBottomChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('surfaceInclanationCanvasContainer', { static: false }) surfaceInclanationContainerRef!: ElementRef<HTMLCanvasElement>;

  //Instances
  private dom: any = {
    mouse_moved: false,
  }

  constructor(
      public scene: SceneService,
      public helpers: HelpersService,
      private route: ActivatedRoute,
      protected auth: AuthService,
      protected models: ModelsService,
      protected ui: UiService,
      protected tutorial: TutorialService,
      protected metadata: MetadataService,
      protected measurements: MeasurementsService,
      protected camera: CameraService,
      protected gps: GpsService,
      protected selection: SelectionService,
      protected insideNavigation: InsideNavigationService,
      protected datasets: DatasetsService,
      protected debug: DebugService,
      protected preload: PreloadService,
      protected preview: PreviewService,
      protected thermal: ThermalService,
      protected level: LevelService,
      protected surfaceInclanation: SurfaceInclanationService,
      protected screenSelection: ScreenSelectionService,
      protected comments: CommentsService,
  ) {}

  async ngOnInit() {
    const guid = this.route.snapshot.paramMap.get('guid');

    //Verify model
    await this.models.setModel( guid );
    if(!this.models.model){ return; }

    //UI
    this.ui.init(guid);
    if(!this.ui.mode){
      this.ui.loading = false;
      return;
    }

    //Init Components
    this.scene.init(this.canvasRef);
    await this.auth.init();
    await this.models.loadSateliteView();
    await this.models.loadModel()
    await this.metadata.load();
    await this.preload.load();
    this.thermal.init();

    //Init only when ui is visible
    if(this.ui.show){
      await this.models.loadSelections();
      await this.preview.load();
      this.comments.init();
      this.surfaceInclanation.init( this.surfaceInclanationRef, this.surfaceInclanationContainerRef, this.surfaceInclanationSideChartRef, this.surfaceInclanationBottomChartRef );

      //Datasets
      if(this.auth.model_manageable){
        await this.datasets.init();
      }

    }

    this.render();
  }
  clearActions(except: string){
    if(this.gps.state && except !== 'GPS'){ this.gps.toggleGPS(); }
    if(this.selection.state && except !== 'selection'){ this.selection.toggleSelection(); }
    if(this.measurements.state && except !== 'measure'){ this.measurements.toggle(); }
    if((this.level.state) && except !== 'level'){ this.level.toggle(); }
    if(this.surfaceInclanation.step && except !== 'surface_inclanation'){ this.surfaceInclanation.toggle(); }
    if(this.comments.step && except !== 'comment'){ this.comments.toggle(); }
  }
  anyActionActive(){
    return this.gps.state || this.selection.state || this.measurements.state || this.level.state;
  }
  render(){
    const { model, model_objects } = this.models;

    if(this.surfaceInclanation.pause_rendering){
      setTimeout(() => {
        requestAnimationFrame(this.render.bind(this));
        console.log('skippingRenders');
      }, 250);
      return;
    }

    if(model_objects?.length === model?.objects.length){
      this.models.runLod();
      this.scene.render()
      this.camera.update()
      this.debug.update()
      this.ui.rendering = true;
      this.ui.loading = false;
    }
    else{
      this.ui.rendering = false;
    }

    requestAnimationFrame(this.render.bind(this));
  }

  ////Listeners

  //Mouse
  @HostListener('document:wheel', ['$event'])
  onWindowScroll(event: WheelEvent) {
    if(!this.helpers.isCanvas(event)){ return; }

    this.camera.onWindowScroll(event);
  }

  @HostListener('document:mousedown', ['$event'])
  onMouseDown(event: MouseEvent) {
    if(!this.helpers.isCanvas(event)){ return; }

    this.dom.mouse_moved = false;

    this.camera.onMouseDown(event);
    this.measurements.highlightMarker(event)
    this.selection.grabMarker(event);
    this.selection.highlightDeleteMarker(event);
    this.screenSelection.setStart(event);
  }

  @HostListener('document:mouseup', ['$event'])
  onMouseUp(event: MouseEvent) {
    if(!this.helpers.isCanvas(event)){ return; }

    this.camera.onMouseUp(event);
    this.measurements.removeMarker(event);
    this.selection.releaseMarker(event);
    this.selection.deHighlightDeleteMarker(event);
    this.screenSelection.confirm(event);

    // Click event
    if(!this.dom.mouse_moved){
      this.measurements.placeMarker(event);
      this.gps.placeMarker(event);
      this.selection.confirmMarker(event);
      this.level.confirm(event);
      this.comments.confirm(event);

      if(!this.anyActionActive()){
        this.insideNavigation.move(event);
      }

    }
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent) {
    this.screenSelection.setEnd(event);

    if(!this.helpers.isCanvas(event)){
      this.gps.locateNonCanvas();
      this.selection.selectNonCanvas();
      this.level.moveIndicatorNonCanvas();
      this.comments.moveIndicatorNonCanvas();
      return;
    }

    this.dom.mouse_moved = true;
    this.measurements.moveIndicator(event);
    this.camera.onMouseMove(event);
    this.gps.locate(event);
    this.selection.select(event);
    this.selection.moveMarker(event);
    this.level.moveIndicator(event);
    this.comments.moveIndicator(event);

    if(!this.anyActionActive()){
      this.insideNavigation.navigate(event);
    }
  }

  //Touch
  @HostListener('document:touchstart', ['$event'])
  onTouchStart(event: TouchEvent) {
    if(!this.helpers.isCanvas(event)){ return; }


    this.camera.onTouchStart(event)
  }

  @HostListener('document:touchend', ['$event'])
  onTouchEnd(event: TouchEvent) {
    this.camera.onTouchEnd(event);
  }

  @HostListener('document:touchmove', ['$event'])
  onTouchMove(event: TouchEvent) {
    if(!this.helpers.isCanvas(event)){ return; }

    this.camera.onTouchMove(event);
  }

  //Window
  @HostListener('window:resize', [])
  onResize() {
    if(!this.ui.rendering){ return; }

    this.scene.resize()
  }

}
