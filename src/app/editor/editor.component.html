<section [ngStyle]="ui.css" >

    <canvas class="render-canvas" #canvas ></canvas>

    <!--Ui-->
    <div *ngIf="ui.show" >

      <!--Envionrment-->
      <div class="position-fixed top-0 left-0 m-3 w-px-250" *ngIf="ui.rendering && !helpers.isMobile() && models.model" >

          <!--Auth User-->
          <div class="card" *ngIf="auth.user" >
              <div class="flex-between">

                  <div>
                      <div class="text-muted font-size-075" >Ingelogd als</div>
                      <div>{{auth.user.name}}</div>
                  </div>

                  <div>
                      <a class="checkbox-label text-center px-3" (click)="auth.logout()" > <i class="fa-solid fa-right-from-bracket"></i> </a>
                  </div>

              </div>
          </div>

          <!--Mode-->
          <div class="card" >
              <div class="form-control align-items-stretch p-05 flex-between rounded-5" *ngIf="models.model._2D" >
                  <label class="m-0 px-2 py-1 flex-center w-100 rounded-4 cursor-pointer" *ngIf="models.model._2D_lite" [ngClass]="ui.mode == '2D LITE' ? 'bg-mark' : ''" (click)="ui.askSetMode('2D LITE')" >
                    <span>2D LITE</span>
                  </label>
                  <label class="m-0 px-2 py-1 flex-center w-100 rounded-4 cursor-pointer" *ngIf="models.model._2D_full" [ngClass]="ui.mode == '2D FULL' ? 'bg-mark' : ''" (click)="ui.askSetMode('2D FULL')" >
                      <span>2D FULL</span>
                  </label>
              </div>
              <div class="form-control align-items-stretch p-05 flex-between rounded-5" *ngIf="models.model._3D" >
                  <label class="m-0 px-2 py-1 flex-center w-100 rounded-4 cursor-pointer" *ngIf="models.model._lite" [ngClass]="ui.mode == 'LITE' ? 'bg-mark' : ''" (click)="ui.askSetMode('LITE')" >
                      <span>3D LITE</span>
                  </label>
                  <label class="m-0 px-2 py-1 flex-center w-100 rounded-4 cursor-pointer" *ngIf="models.model._full" [ngClass]="ui.mode == 'FULL' ? 'bg-mark' : ''" (click)="ui.askSetMode('FULL')" >
                      <div class="" ></div>
                      <span>3D FULL</span>
                  </label>
              </div>
          </div>

          <!--Perspective-->
          <div class="card" *ngIf="models.model.inside && !ui.is2D()" >
            <div class="form-control align-items-stretch p-05 flex-between rounded-5" >
              <label class="m-0 px-2 py-1 flex-center w-100 rounded-4 cursor-pointer" [ngClass]="ui.perspective == 'ORBIT' ? 'bg-mark' : ''" (click)="insideNavigation.selectView('ORBIT'); metadata.load()" >
                <i class="fa-solid fa-cube mr-1"></i>
                <span>Orbit</span>
              </label>
              <label class="m-0 px-2 py-1 flex-center w-100 rounded-4 cursor-pointer" [ngClass]="ui.perspective == 'FIRST_PERSON' ? 'bg-mark' : ''" (click)="insideNavigation.selectView('FIRST_PERSON'); ; metadata.load()" >
                <i class="fa-solid fa-person-walking mr-1"></i>
                <span>First-Person</span>
              </label>
            </div>
          </div>

          <!--Render distance, Zoom-->
          <div class="card" >
              <div class="my-2" *ngIf="models.model_objects.length > 1" >
                  <div>Renderafstand</div>
                  <input min="0" [step]="ui.render_distance_basis" [max]="ui.render_distance_basis * 10" type="range" class="form-control-range" [(ngModel)]="ui.render_distance" >
              </div>
              <div class="my-2">
                  <div>Zoom snelheid</div>
                  <input min="0" step="0.005" max="0.1" type="range" class="form-control-range" [(ngModel)]="ui.zoom_speed" >
              </div>
          </div>

          <!--Objects: Models-->
          <div class="card my-1" *ngIf="models.model_objects.length" >
            <div class="flex-between cursor-pointer checkbox-label px-1 py-0" (click)="ui.toggle('models')" >
              <div>Modellen</div>
              <a class="py-1 px-2" > <i class="fa-solid fa-chevron-down" [ngClass]="ui.toggles.models ? 'rotate-180' : 'rotate-0'" ></i> </a>
            </div>
            <div *ngIf="ui.toggles.models" class="px-1 pt-1 mt-1 font-size-08 border-top max-h-300 overflow-auto scroll" >
              <div *ngFor="let model_object of models.model_objects" >
                <div class=" flex-align {{!model_object.object.visible ? 'text-muted' : ''}}" >

                  <div class="w-100" >{{model_object.name}}</div>

                  <div class="d-flex" >
                    <div class="checkbox-label text-center px-2" (click)="models.toggleObjectVisibility(model_object)" >
                      <i *ngIf="model_object.object.visible" class="fa-solid fa-eye m-0"></i>
                      <i *ngIf="!model_object.object.visible" class="fa-solid fa-eye-slash m-0"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!--Objects: Selections: floor-->
          <div class="card my-1" *ngIf="models.getSelections('floor').length" >
            <div class="flex-between cursor-pointer checkbox-label px-1 py-0" (click)="ui.toggle('floor_selections')" >
              <div>Vloer Selecties</div>
              <a class="py-1 px-2" > <i class="fa-solid fa-chevron-down" [ngClass]="ui.toggles.floor_selections ? 'rotate-180' : 'rotate-0'" ></i> </a>
            </div>
            <div *ngIf="ui.toggles.floor_selections" class="px-1 pt-1 mt-1 font-size-08 border-top max-h-300 overflow-auto scroll" >
              <div *ngFor="let selection_model of models.getSelections('floor')" >
                <div class=" flex-align {{!selection_model.object.visible ? 'text-muted' : ''}}" >


                  <a *ngIf="auth.model_manageable" class="text-danger px-2 cursor-pointer" (click)="selection.deleteSelection(selection_model)" > <i class="fa-solid fa-xmark"></i> </a>

                  <div class="w-100" >{{selection_model.name}}</div>
                  <div class="d-flex" >
                    <div class="checkbox-label text-center px-2" (click)="models.toggleObjectDepthTest(selection_model)" >
                      <i *ngIf="selection_model.depth_test" class="fa-solid fa-arrows-up-to-line"></i>
                      <i *ngIf="!selection_model.depth_test" class="fa-solid fa-arrow-down-up-across-line"></i>
                    </div>
                    <div class="checkbox-label text-center px-2" (click)="models.toggleObjectVisibility(selection_model)" >
                      <i *ngIf="selection_model.object.visible" class="fa-solid fa-eye m-0"></i>
                      <i *ngIf="!selection_model.object.visible" class="fa-solid fa-eye-slash m-0"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!--Objects: Selections: datasets-->
          <div class="card my-1" *ngIf="models.getSelections('dataset').length" >
            <div class="flex-between cursor-pointer checkbox-label px-1 py-0" (click)="ui.toggle('dataset_selections')" >
              <div>Dataset Selecties</div>
              <a class="py-1 px-2" > <i class="fa-solid fa-chevron-down" [ngClass]="ui.toggles.dataset_selections ? 'rotate-180' : 'rotate-0'" ></i> </a>
            </div>
            <div *ngIf="ui.toggles.dataset_selections" class="px-1 pt-1 mt-1 max-h-300 overflow-auto scroll" >

              <div class="btn btn-block btn-inverse-success btn-sm btn-w-100" (click)="this.helpers.redirectPortal('models/selections/export/excel/'+models.model.guid, true)">
                <span class="mr-2"><i class="fa-solid fa-file-excel"></i></span>
                <span>Excel</span>
              </div>

              <!--Groups-->
              <div *ngFor="let selection_group of models.getSelectionsGroups('dataset_row_id'); let g = index" >

                <div class="flex-between mt-2 pt-1 font-size-09 border-bottom" >
                  <div class="line-height-11 max-w-175 nobr overflow-hidden" >
                    <div>{{models.getSelectionsByGroup('dataset_row_id', selection_group)[0].dataset_row?.name}}</div>
                    <div class="font-size-075 font-weight-bold text-muted" >{{models.getSelectionsGroupSurfaceArea('dataset_row_id', selection_group)}}m²</div>
                  </div>

                  <div class="ml-auto d-flex font-size-075">
                    <div class="checkbox-label p-1" *ngIf="models.selectionGroupIsVisible('dataset_row_id', selection_group)" (click)="models.toggleSelectionGroupDepthTest('dataset_row_id', selection_group, !models.selectionGroupHasDepthTest('dataset_row_id', selection_group))" >
                      <i *ngIf="models.selectionGroupHasDepthTest('dataset_row_id', selection_group)" class="fa-solid fa-arrows-up-to-line m-0"></i>
                      <i *ngIf="!models.selectionGroupHasDepthTest('dataset_row_id', selection_group)" class="fa-solid fa-arrow-down-up-across-line m-0"></i>
                    </div>
                    <div class="checkbox-label p-1" (click)="models.toggleSelectionGroupVisibility('dataset_row_id', selection_group, !models.selectionGroupIsVisible('dataset_row_id', selection_group))" >
                      <i *ngIf="models.selectionGroupIsVisible('dataset_row_id', selection_group)" class="fa-solid fa-eye m-0"></i>
                      <i *ngIf="!models.selectionGroupIsVisible('dataset_row_id', selection_group)" class="fa-solid fa-eye-slash m-0"></i>
                    </div>
                  </div>
                </div>

                <!--Group Selections-->
                <div *ngFor="let selection_model of models.getSelectionsByGroup('dataset_row_id', selection_group)" >

                  <div class="flex-between my-1 font-size-08" [ngClass]="selection_model.object.visible ? '' : 'opacity-50'" >
                    <div class="flex-align">
                      <div class="w-px-5 h-px-25 rounded-10 mr-2" [ngStyle]="{'background-color': selection_model.color}"  ></div>
                      <div class="line-height-11" >
                        <div class="nobr max-w-150 overflow-hidden" >{{selection_model.name}}</div>
                        <div class="font-size-075 font-weight-bold text-muted" >{{selection_model.surface_area}}m²</div>
                      </div>
                    </div>

                    <div class="ml-auto d-flex font-size-065">
                      <div class="checkbox-label text-danger p-1" *ngIf="auth.authenticated && auth.model_manageable" (click)="selection.deleteSelection(selection_model)" >
                        <i class="fa-solid fa-trash-can m-0"></i>
                      </div>
                      <div class="checkbox-label p-1" *ngIf="selection_model.object.visible" (click)="models.toggleObjectDepthTest(selection_model)" >
                        <i *ngIf="selection_model.depth_test" class="fa-solid fa-arrows-up-to-line m-0"></i>
                        <i *ngIf="!selection_model.depth_test" class="fa-solid fa-arrow-down-up-across-line m-0"></i>
                      </div>
                      <div class="checkbox-label p-1" (click)="models.toggleObjectVisibility(selection_model)" >
                        <i *ngIf="selection_model.object.visible" class="fa-solid fa-eye m-0"></i>
                        <i *ngIf="!selection_model.object.visible" class="fa-solid fa-eye-slash m-0"></i>
                      </div>
                    </div>

                  </div>

                </div>
              </div>

            </div>

          </div>

          <!--Objects: objects-->
          <div class="card my-1" *ngIf="models.objects.length" >
            <div class="flex-between cursor-pointer checkbox-label px-1 py-0" (click)="ui.toggle('objects')" >
              <div>Objecten</div>
              <a class="py-1 px-2" > <i class="fa-solid fa-chevron-down" [ngClass]="ui.toggles.objects ? 'rotate-180' : 'rotate-0'" ></i> </a>
            </div>
            <div *ngIf="ui.toggles.objects" class="px-1 pt-1 mt-1 font-size-08 border-top max-h-300 overflow-auto scroll" >
              <div *ngFor="let object of models.objects" >
                <div class=" flex-align {{!object.visible ? 'text-muted' : ''}}" >

                  <div class="w-100" >{{object.name}}</div>
                  <div class="d-flex" >
                    <div class="checkbox-label text-center px-2" (click)="scene.transform(object)" [ngClass]="scene.transformControls.object?.uuid == object.uuid ? 'bg-mark' : ''" >
                      <i class="fa-brands fa-unity"></i>
                    </div>
                    <div class="checkbox-label text-center px-2" (click)="models.toggleObjectVisibility(object)" >
                      <i *ngIf="object.visible" class="fa-solid fa-eye m-0"></i>
                      <i *ngIf="!object.visible" class="fa-solid fa-eye-slash m-0"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!--Files-->
          <div class="card my-1" *ngIf="models.files.length" >
            <div class="flex-between cursor-pointer checkbox-label px-1 py-0" (click)="ui.toggle('files')" >
              <div>Bijlagen</div>
              <a class="py-1 px-2" > <i class="fa-solid fa-chevron-down" [ngClass]="ui.toggles.files ? 'rotate-180' : 'rotate-0'" ></i> </a>
            </div>
            <div *ngIf="ui.toggles.files" class="px-1 pt-1 mt-1 font-size-08 border-top max-h-300 overflow-auto scroll" >

              <div *ngFor="let file of models.files" class="p-05 rounded-3 hover-mark cursor-pointer" (click)="helpers.redirectPortal('file/'+file.src, true)" >
                <div class="flex-align overflow-hidden">

                  <div class="mr-2" ><img [src]="file.icon" height="30" > </div>
                  <div class="line-height-11" >
                    <div class="nobr" >{{file.name}}</div>
                    <div class="font-size-075 font-weight-bold text-muted" >{{file.size.display}}</div>
                  </div>


                </div>
              </div>

            </div>
          </div>

          <!--Comments-->
          <div class="card my-1" *ngIf="comments.comments.length" >
            <div class="flex-between cursor-pointer checkbox-label px-1 py-0" (click)="ui.toggle('comments')" >
              <div>Opmerkingen</div>
              <a class="py-1 px-2" > <i class="fa-solid fa-chevron-down" [ngClass]="ui.toggles.comments ? 'rotate-180' : 'rotate-0'" ></i> </a>
            </div>
            <div *ngIf="ui.toggles.comments" class="px-1 pt-1 mt-1 font-size-075 border-top max-h-300 overflow-auto scroll" >

              <div *ngFor="let comments_date of comments.getDates()" class="pb-2" >

                <div class="flex-between text-muted font-weight-bolder nobr" >
                  <div class="border-top w-100 mr-2" ></div>
                  <div>{{comments_date.day}} {{comments_date.monthShort}} {{comments_date.year}}</div>
                  <div class="border-top w-100 ml-2" ></div>
                </div>

                <div *ngFor="let comment of comments.commentsByDate(comments_date.formattedISO)" class="p-05 overflow-hidden" >
                  <div class="flex-between">
                    <div class="font-size-075 font-weight-bold text-muted" >{{comment.user.name}}</div>
                    <div class="ml-auto d-flex font-size-065">
                      <div class="checkbox-label text-danger p-1" *ngIf="comments.can_comment" (click)="comments.deleteComment(comment)" >
                        <i class="fa-solid fa-trash-can m-0"></i>
                      </div>
                      <div class="checkbox-label p-1" (click)="comments.showComment(comment)" >
                        <i class="fa-solid fa-eye m-0"></i>
                      </div>
                    </div>
                  </div>
                  <div class="line-height-11" >
                    <div>{{comment.comment}}</div>
                  </div>
                </div>


              </div>


            </div>
          </div>

          <!--Google Maps Area-->
          <div class="card" *ngIf="models.sprites.google_maps" >
              <div class="text-muted" >Omgeving</div>
              <div class="form-control align-items-stretch p-05 flex-between rounded-5 nobr">
                <label class="m-0 px-1 flex-center w-100 rounded-4 cursor-pointer" [ngClass]="models.sprites.google_maps.area == 60 ? 'bg-mark' : ''" (click)="models.updateSateliteArea(60)" >
                  <span>60 m<sup>2</sup></span>
                </label>
                <label class="m-0 px-1 flex-center w-100 rounded-4 cursor-pointer font-size-075" [ngClass]="models.sprites.google_maps.area == 120 ? 'bg-mark' : ''" (click)="models.updateSateliteArea(120)" >
                  <span>120 m<sup>2</sup></span>
                </label>
                <label class="m-0 px-1 flex-center w-100 rounded-4 cursor-pointer font-size-075" [ngClass]="models.sprites.google_maps.area == 240 ? 'bg-mark' : ''" (click)="models.updateSateliteArea(240)" >
                  <span>240 m<sup>2</sup></span>
                </label>
                <label class="m-0 px-1 flex-center w-100 rounded-4 cursor-pointer font-size-075" [ngClass]="models.sprites.google_maps.area == 480 ? 'bg-mark' : ''" (click)="models.updateSateliteArea(480)" >
                  <span>480 m<sup>2</sup></span>
                </label>
                <label class="m-0 px-1 flex-center w-100 rounded-4 cursor-pointer font-size-075" [ngClass]="models.sprites.google_maps.area == 960 ? 'bg-mark' : ''" (click)="models.updateSateliteArea(960)" >
                  <span>960 m<sup>2</sup></span>
                </label>
              </div>
          </div>

          <!--Cube-->
          <div class="position-cube-container" *ngIf="!ui.is2D() && ui.perspective === 'ORBIT'" >
              <div class="position-cube" >
                  <div class="position-cube position-cube-face position-cube-top" (click)="camera.setCameraAngle('TOP')" >BOVEN</div>
                  <div class="position-cube position-cube-face position-cube-left" (click)="camera.setCameraAngle('LEFT')" >WEST</div>
                  <div class="position-cube position-cube-face position-cube-front" (click)="camera.setCameraAngle('FRONT')" >ZUID</div>
              </div>
          </div>

      </div>

      <!--Mobile Envionrment-->
      <div class="position-fixed top-0 left-0 m-3 w-px-100" *ngIf="ui.rendering && helpers.isMobile() && models.model" >
        <!--Mode-->
        <div class="card" >
          <div class="form-control align-items-stretch p-05 flex-between flex-colummn rounded-5">
            <label class="m-0 px-2 py-1 flex-center w-100 rounded-4 cursor-pointer" *ngIf="models.model._2D_mobile" [ngClass]="ui.mode == '2D MOBILE' ? 'bg-mark' : ''" (click)="ui.askSetMode('2D MOBILE')" >
              <span>2D</span>
            </label>
            <label class="m-0 px-2 py-1 flex-center w-100 rounded-4 cursor-pointer" *ngIf="models.model._mobile" [ngClass]="ui.mode == 'MOBILE' ? 'bg-mark' : ''" (click)="ui.askSetMode('MOBILE')" >
              <span>3D</span>
            </label>
          </div>
        </div>

        <!--Perspective-->
        <div class="card" *ngIf="models.model.inside && !ui.is2D()" >
          <div class="form-control align-items-stretch p-05 flex-between rounded-5" >
            <label class="m-0 px-2 py-1 flex-center w-100 rounded-4 cursor-pointer" [ngClass]="ui.perspective == 'ORBIT' ? 'bg-mark' : ''" (click)="insideNavigation.selectView('ORBIT'); metadata.load()" >
              <i class="fa-solid fa-cube mr-1"></i>
            </label>
            <label class="m-0 px-2 py-1 flex-center w-100 rounded-4 cursor-pointer" [ngClass]="ui.perspective == 'FIRST_PERSON' ? 'bg-mark' : ''" (click)="insideNavigation.selectView('FIRST_PERSON'); ; metadata.load()" >
              <i class="fa-solid fa-person-walking mr-1"></i>
            </label>
          </div>
        </div>

      </div>

      <!--Actions-->
      <div class="position-fixed top-0 right-0 m-3 w-px-250" *ngIf="ui.rendering && !helpers.isMobile() && models.model"  >

          <!--Tutorial-->
          <div class="card" *ngIf="!ui.is2D()" >
              <div class="checkbox-label flex-between" (click)="tutorial.open()" >
                  <span class="mr-2" >Uitleg</span>
                  <span><i class="fa-solid fa-circle-info"></i></span>
              </div>
          </div>

          <!--GPS-->
          <div class="card" *ngIf="this.models.model?.coordinates" >

            <div class="checkbox-label flex-between" [ngClass]="gps.state ? 'bg-mark' : ''" (click)="clearActions('GPS'); gps.toggleGPS()" >
              <span class="mr-2" >GPS</span>
              <span><i class="fa-solid fa-location-dot"></i></span>
            </div>

            <!--Radius-->
            <div class="my-2" *ngIf="gps.state" >
              <div>Indicator radius</div>
              <input min="0.005" step="0.001" max="1" type="range" class="form-control-range" [(ngModel)]="gps.indicator_radius" (change)="gps.setIndicatorRadius()" >
            </div>

          </div>
          <div class="card" *ngIf="gps.state || gps.markers.length" >
            <!--Locaties-->
            <div  class="my-2" >

              <!--Label-->
              <div class="text-muted flex-between" >
                <span>Locaties</span>
                <span class="font-size-08 cursor-pointer px-1" (click)="gps.clearIndicatorPoints()" ><i class="fa-solid fa-rotate-right m-0"></i></span>
              </div>

              <!--GPS Locations-->
              <div class="overflow-auto max-h-200 scroll my-2" >

                <div *ngFor="let location of gps.markers; let m = index" class="flex-align px-1 font-size-075 "
                     (mouseenter)="gps.highlightRemoveMarkerByIndex(m)"
                     (mouseleave)="gps.dehighlightMarkerByIndex(m)"
                >

                  <div class="mr-2 w-100" >
                    <div>{{location.userData['gps_dms_lat']}} {{location.userData['gps_dms_lon']}}</div>
                  </div>

                  <div class="d-flex" >
                    <a class="py-1 px-2 checkbox-label"
                       target="_blank"
                       [href]="'https://www.google.com/maps?q='+location.userData['gps_dms_lat']+' '+location.userData['gps_dms_lon']"
                    > <i class="fa-solid fa-map-location-dot"></i> </a>
                    <a class="py-1 px-2 checkbox-label"
                       (click)="helpers.copyString(location.userData['gps_dms_lat']+' '+location.userData['gps_dms_lon'])"
                    > <i class="fa-solid fa-copy"></i> </a>
                    <a class="py-1 px-2 checkbox-label"
                       (click)="gps.removeMarkerByIndex(m)"
                    > <i class="fa-solid fa-xmark"></i> </a>
                  </div>


                </div>

                <div class="border-top flex-align p-1" *ngIf="gps.state" >
                  <div class="font-size-075 text-muted" >
                    {{gps.indicator.userData?.['gps_dms_lat']}} {{gps.indicator.userData?.['gps_dms_lon']}}
                  </div>
                </div>

              </div>

            </div>
          </div>


          <!--Selections-->
          <div class="card" *ngIf="auth.authenticated" >

            <div class="checkbox-label flex-between" [ngClass]="selection.state ? 'bg-mark' : ''" (click)="clearActions('selection'); selection.toggleSelection()" >
              <span class="mr-2" >Selecteren</span>
              <span><i class="fa-solid fa-vector-square"></i></span>
            </div>

            <div *ngIf="selection.state" >

              <!--Radius-->
              <div class="my-2">
                <div>Indicator radius</div>
                <input min="0.001" step="0.001" max="0.75" type="range" class="form-control-range" [(ngModel)]="selection.indicator_radius" (input)="selection.setIndicatorRadius()" >
              </div>

              <!--Measurements-->
              <div  >
                <div class="flex-between" >
                  <span>Totaal:</span>
                  <span>{{selection.polygon_circumference}}m</span>
                </div>
                <div class="flex-between" >
                  <span>Oppervlakte:</span>
                  <span>{{selection.polygon_surface_area}}m²</span>
                </div>
              </div>

              <!--Polygon-->
              <div class="d-flex my-2 font-size-085" *ngIf="selection.polygon" >
                <div class="checkbox-label text-center px-2 flex-center" (click)="scene.transform(selection.polygon, selection.markers)" [ngClass]="scene.isTransformerAttached(selection.polygon.uuid) ? 'bg-mark' : ''" >
                  <i class="fa-brands fa-unity"></i>
                </div>
                <div class="checkbox-label text-center px-2 flex-center" (click)="models.toggleObjectVisibility(selection.polygon)" >
                  <i *ngIf="selection.polygon.visible" class="fa-solid fa-eye m-0"></i>
                  <i *ngIf="!selection.polygon.visible" class="fa-solid fa-eye-slash m-0"></i>
                </div>
                <div class="checkbox-label text-center px-2" (click)="models.toggleObjectDepthTest(selection.polygon)" >
                  <i *ngIf="selection.polygon.material.depthTest" class="fa-solid fa-arrows-up-to-line"></i>
                  <i *ngIf="!selection.polygon.material.depthTest" class="fa-solid fa-arrow-down-up-across-line"></i>
                </div>
              </div>

              <div class="full-divider"></div>

              <div *ngIf="auth.authenticated && auth.model_manageable" >

                <!--Name-->
                <div class="my-2">
                  <div>Naam</div>
                  <input class="form-control" [(ngModel)]="selection.name" >
                </div>

                <!--Type-->
                <div class="my-2">
                  <div>Type</div>
                  <select class="form-control" [(ngModel)]="selection.type" >
                    <option value="" disabled >Selecteer type</option>
                    <option value="dataset" >Dataset</option>
                    <option *ngIf="models.model.inside" value="floor">Binnenruimte vloer</option>
                  </select>
                </div>

                <!--Dataset-->
                <div class="my-2" *ngIf="selection.type == 'dataset'" >
                  <div>Dataset</div>
                  <select class="form-control" [(ngModel)]="selection.dataset_id" >
                    <option value="0" disabled >Selecteer Dataset</option>
                    <option *ngFor="let dataset of datasets.datasets" [value]="dataset.id" >{{dataset.name}}</option>
                  </select>
                </div>

                <!--Dataset Row-->
                <div class="my-2" *ngIf="selection.type == 'dataset' && selection.dataset_id" >
                  <div>Item</div>
                  <select class="form-control" [(ngModel)]="selection.dataset_row_id" (change)="selection.datasetRowSelected()" >
                    <option value="0" disabled >Selecteer Item</option>
                    <option *ngFor="let row of datasets.getRows(selection.dataset_id)" [value]="row.id" >{{row.name}}</option>
                  </select>
                </div>

                <div class="mb-2 mt-4 text-center" *ngIf="selection.isSelectionValid()" >
                  <a class="btn btn-block btn-sm btn-inverse-success rounded-pill" (click)="selection.confirmSelection()" >Bevestigen</a>
                </div>

              </div>

            </div>

          </div>

          <!--Comments-->
          <div class="card" *ngIf="comments.can_comment" >

            <div class="checkbox-label flex-between" [ngClass]="comments.step ? 'bg-mark' : ''" (click)="clearActions('comment'); comments.toggle()" >
              <span class="mr-2" >Opmerking</span>
              <span><i class="fa-solid fa-comment"></i></span>
            </div>

            <div *ngIf="!comments.loading" >
              <!--Select point of intereset-->
              <div *ngIf="comments.step === 'select'" >
                <div class="my-2" >
                  <div class="text-center text-muted" >Selecteer Point of Interest</div>
                </div>
              </div>

              <!--Data-->
              <div *ngIf="comments.step === 'confirm'" >

                <div class="my-2 position-relative">
                  <input class="form-control pr-5" maxlength="255" placeholder="Opmerking" [(ngModel)]="comments.comment" >
                  <div class="flex-center font-size-065 h-100 mr-2 position-absolute right-0 text-muted top-0" >{{comments.comment.length}} / 255</div>
                </div>

                <div class="mb-2 mt-4 text-center"
                  [class.opacity-50]="!comments.commentIsValid()"
                  [class.pointer-event-none]="!comments.commentIsValid()"
                >
                  <a class="btn btn-block btn-sm btn-inverse-success rounded-pill" (click)="comments.storeComment()" >Bevestigen</a>
                </div>

              </div>
            </div>

            <div *ngIf="comments.loading" class="text-center my-2" >
              <div class="spinner-border spinner-border-md text-warning" >
                <span class="sr-only"></span>
              </div>
            </div>

          </div>

          <!--Level-->
          <div class="card">

            <div class="checkbox-label flex-between" [ngClass]="(level.state) ? 'bg-mark' : ''" (click)="clearActions('level'); level.toggle()" >
              <span class="mr-2" >Waterpas</span>
              <span><i class="fa-solid fa-ruler"></i></span>
            </div>

            <div class="my-2" *ngIf="!ui.is2D() && (level.state)" >
              <div>Type:</div>
              <div class="form-control align-items-stretch p-05 flex-between font-size-075 rounded-5">
                <div class="m-0 px-2 py-1 text-center w-100 rounded-4 cursor-pointer" (click)="level.toggleType('horizontal')" [ngClass]="level.level_type == 'horizontal' ? 'bg-mark' : ''" >
                  <span>Horizontaal</span>
                </div>
                <div class="m-0 px-2 py-1 text-center w-100 rounded-4 cursor-pointer" (click)="level.toggleType('vertical')" [ngClass]="level.level_type == 'vertical' ? 'bg-mark' : ''" >
                  <span>Verticaal</span>
                </div>
              </div>
            </div>

            <!--Radius-->
            <div class="my-2" *ngIf="level.state" >
              <div>Indicator radius</div>
              <input min="0.001" step="0.001" max="0.75" type="range" class="form-control-range" [(ngModel)]="level.indicator_radius" (change)="level.setIndicatorRadius()" >
            </div>

          </div>
          <div class="card" *ngIf="level.state || level.triangles.length" >

            <!--Triangles-->
            <div  class="my-2" >

              <!--Label-->
              <div class="text-muted flex-between" >
                <span>Afschotten</span>
                <span class="font-size-08 cursor-pointer px-1" (click)="level.clearTriangles()" ><i class="fa-solid fa-rotate-right m-0"></i></span>
              </div>

              <!--Triangles-->
              <div class="overflow-auto max-h-200 scroll" >
                <div *ngFor="let triangle of level.triangles; let d = index" class="checkbox-label flex-between p-1" (click)="level.removeTriangle(d)" (mouseenter)="level.highlightTriangle(d)" (mouseleave)="level.dehighlightTriangle(d)" >
                  <span class="font-size-08 text-muted" > <i class="fa-solid fa-xmark"></i> </span>
                  <div class="flex-align">
                    <span>{{triangle.getAngles().a}}deg</span>
                    <span class="text-muted mx-2" >|</span>
                    <span> {{triangle.getDistances().short}}m</span>
                  </div>
                </div>
              </div>

            </div>

          </div>

          <!--Measurements-->
          <div class="card">

              <div class="checkbox-label flex-between" [ngClass]="(measurements.length_state || measurements.volume_step) ? 'bg-mark' : ''" (click)="clearActions('measure'); measurements.toggle()" >
                  <span class="mr-2" >Meten</span>
                  <span><i class="fa-solid fa-ruler-combined"></i></span>
              </div>

              <!--Model Quality-->
              <div class="my-2" *ngIf="!ui.is2D() && (measurements.length_state || measurements.volume_step)">
                <div class="form-control align-items-stretch p-05 flex-between font-size-075 rounded-5">
                  <label class="m-0 px-2 py-1 text-center w-100 rounded-4 cursor-pointer"
                         [ngClass]="measurements.mode == 'length' ? 'bg-mark' : ''"
                         (click)="measurements.setMode('length')"
                  >
                    <img src="/svg/length.svg" class="h-100">
                  </label>
                  <label class="m-0 px-2 py-1 text-center w-100 rounded-4 cursor-pointer"
                         [ngClass]="measurements.mode == 'volume' ? 'bg-mark' : ''"
                         (click)="measurements.setMode('volume')"
                  >
                    <img src="/svg/cube.svg" class="h-100">
                  </label>
                </div>
              </div>

              <div *ngIf="measurements.mode == 'length' && measurements.length_state" >

                <!--Model Quality-->
                <div class="my-2" *ngIf="!ui.is2D() && models.model_objects.length > 1" >
                  <div>Meten in:</div>
                  <div class="form-control align-items-stretch p-05 flex-between font-size-075 rounded-5">
                      <label class="m-0 px-2 py-1 text-center w-100 rounded-4 cursor-pointer"
                        [ngClass]="measurements.model_quality == 'low' ? 'bg-mark' : ''"
                        (click)="measurements.setMeasureModelQuality('low')"
                      >
                          <span>Lage kwaliteit</span>
                      </label>
                      <label class="m-0 px-2 py-1 text-center w-100 rounded-4 cursor-pointer"
                             [ngClass]="measurements.model_quality == 'high' ? 'bg-mark' : ''"
                             (click)="measurements.setMeasureModelQuality('high')"
                      >
                          <span>Hoge kwaliteit</span>
                      </label>
                  </div>
                </div>

                <!--Radius-->
                <div class="my-2" >
                  <div>Indicator radius</div>
                  <input min="0.001" step="0.001" max="0.75" type="range" class="form-control-range" [(ngModel)]="measurements.indicator_radius" (change)="measurements.setMeasureIndicatorRadius()" >
                </div>

              </div>
              <div *ngIf="measurements.mode == 'volume' && measurements.volume_step" >

                <!--Select Walls-->
                <div *ngIf="measurements.volume_step == 'select'">

                  <div class="my-2">


                    <div class="flex-between" *ngIf="measurements.volume_walls.length" >
                      <span>Modelmuren instellen</span>
                      <span class="btn btn-sm text-white" (click)="measurements.volumeHelp()" ><i class="fa-solid fa-question"></i></span>
                    </div>
                    <div class="cursor-pointer flex-align hover-bg-light hover-mark line-height-1 p-1 rounded-4"
                      *ngFor="let wall of measurements.volume_walls; let i = index"
                      (click)="measurements.toggleWallType(wall)"
                      (mouseenter)="measurements.highlightWall(wall)"
                      (mouseleave)="measurements.dehighlightWall(wall)"
                    >
                      <div class="w-px-5 h-px-25 rounded-10 mr-2"
                           [ngStyle]="{'background-color': (wall.userData['wall_type'] == 'barrier') ? '#fb8300' : '#5555FF'}"
                      ></div>
                      <div class="font-size-08">
                        <div>Muur {{i+1}}</div>
                        <div class="font-size-075 text-muted">{{wall.userData['wall_type'] == 'barrier' ? 'Selectiemuur' : 'Modelmuur'}}</div>
                      </div>
                    </div>

                  </div>

                  <div class="my-2 text-center">
                    <div class="flex-align">
                      <a class="btn btn-block btn-inverse-success btn-sm rounded-pill"
                         (click)="measurements.confirmVolumeSelection()"
                         [class.opacity-50]="!measurements.isVolumeEnclosed()"
                         [class.pointer-event-none]="!measurements.isVolumeEnclosed()"
                      >Verder</a>
                      <a class="btn btn-sm btn-inverse-danger rounded-pill ml-1 w-25"
                         (click)="measurements.selectVolumeArea()"
                         [class.opacity-50]="!measurements.markers.length"
                         [class.pointer-event-none]="!measurements.markers.length"
                      > <i class="fa-solid fa-rotate-right"></i> </a>
                    </div>
                  </div>
                  <div class="text-center text-muted font-size-07" *ngIf="!measurements.volume_walls.length" >
                    <span>Selecteer het gebied door punten te plaatsen</span>
                  </div>
                  <div class="text-center text-muted font-size-07" *ngIf="measurements.volume_walls.length && !measurements.isVolumeEnclosed()" >
                    <span>Sluit het gebied af door het eerste en laatste punt met elkaar te verbinden</span>
                  </div>

                </div>

                <!--Confirm Precision-->
                <div *ngIf="measurements.volume_step == 'confirm'" >
                  <!--Precision-->
                  <div class="my-2">
                    <div>Scan nauwkeurigheid</div>
                    <select class="form-control" [(ngModel)]="measurements.volume_precision" >
                      <option [ngValue]=".75" *ngIf="auth.user?.is_developer" >Developer's Option</option>
                      <option [ngValue]=".3" >Heel laag</option>
                      <option [ngValue]=".25" >Laag</option>
                      <option [ngValue]=".2" >Gemiddeld</option>
                      <option [ngValue]=".15" >Hoog</option>
                      <option [ngValue]=".1" >Heel hoog</option>
                    </select>
                  </div>
                  <div class="my-2 text-center"   >
                    <div class="flex-align">
                      <a class="btn btn-block btn-inverse-success btn-sm rounded-pill"
                         (click)="measurements.confirmVolumePrecision()"
                      >Starten</a>
                    </div>
                  </div>
                </div>

                <!--Compute mesh-->
                <div *ngIf="measurements.volume_step == 'compile_mesh'" class="text-center" >
                  <div class="spinner-border spinner-border-md" >
                    <span class="sr-only"></span>
                  </div>
                  <div class="text-muted my-2">Computing optimized mesh</div>
                </div>

                <!--Calculate volume-->
                <div *ngIf="measurements.volume_step == 'calculate'"  >
                  <div class="font-size-075" >
                    <span>{{measurements.volume_duration}}</span>
                    <span class="mx-1 text-muted" >/</span>
                    <span>{{measurements.volume_complete_eta}}</span>
                  </div>
                  <div class="flex-align">
                    <div class="loading-bar-container w-100">
                      <div class="loading-bar"
                           [style.width]="measurements.volume_steps_percentage + '%'"
                      ></div>
                    </div>
                    <div class="w-ch-6 text-center nobr overflow-hidden" >{{measurements.volume_steps_percentage}}%</div>
                  </div>
                </div>

                <!--Results-->
                <div *ngIf="measurements.volume_step == 'results'"  >
                  <div class="flex-between my-2" >
                    <span>Inhoud:</span>
                    <span>{{measurements.volume}}m³</span>
                  </div>
                  <div class="flex-between my-2" >
                    <div>Weergave:</div>
                    <div class="form-control align-items-stretch p-05 flex-between font-size-065 rounded-5 ml-2">
                      <div class="m-0 px-2 py-1 text-center w-100 rounded-4 cursor-pointer"
                           [ngClass]="measurements.volume_visualisation == 'model' ? 'bg-mark' : ''"
                           (click)="measurements.setVolumeVisualisation('model')"
                      >
                        <span>Model</span>
                      </div>
                      <div class="m-0 px-2 py-1 text-center w-100 rounded-4 cursor-pointer"
                           [ngClass]="measurements.volume_visualisation == 'volume' ? 'bg-mark' : ''"
                           (click)="measurements.setVolumeVisualisation('volume')"
                      >
                        <span>Volume</span>
                      </div>
                    </div>
                  </div>
                  <div class="text-right text-muted font-size-07 my-2" >
                    <span>Berekend in {{measurements.volume_duration}}s</span>
                  </div>
                </div>

              </div>


          </div>
          <div class="card" *ngIf="measurements.length_state || measurements.distances.length" >

              <!--Afmetingen-->
              <div  class="my-2" >

                  <!--Label-->
                  <div class="text-muted flex-between" >
                      <span>Afmetingen</span>
                      <span class="font-size-08 cursor-pointer px-1" (click)="measurements.clearLength()" ><i class="fa-solid fa-rotate-right m-0"></i></span>
                  </div>

                  <!--Measurements-->
                  <div class="overflow-auto max-h-200 scroll" >
                      <div *ngFor="let distance of measurements.distances; let d = index" class="checkbox-label flex-between p-1" (click)="measurements.removeMeasure(d)" (mouseenter)="measurements.highlightMeasure(d)" (mouseleave)="measurements.dehighlightMeasure(d)" >
                          <span class="font-size-08 text-muted" > <i class="fa-solid fa-xmark"></i> </span>
                          <span>{{distance}}m</span>
                      </div>
                  </div>

                  <!--Total distance-->
                  <div  >
                      <div class="full-divider"></div>
                      <div class="flex-between" >
                          <span>Totaal:</span>
                          <span>{{measurements.total_distance}}m</span>
                      </div>
                      <div class="flex-between" >
                          <span>Oppervlakte:</span>
                          <span>{{measurements.total_surface}}m²</span>
                      </div>
                  </div>

              </div>

          </div>

          <!--Lightning-->
          <div class="card" >

              <div class="checkbox-label flex-between" [ngClass]="scene.lightning.state ? 'bg-mark' : ''" (click)="scene.toggleLightning()" >
                  <span class="mr-2" >Belichting</span>
                  <span><i class="fa-regular fa-lightbulb"></i></span>
              </div>

              <!--Radius-->
              <div class="my-2" *ngIf="scene.lightning.state" >
                  <div>Intensiteit</div>
                  <input min="0.1" step="0.1" max="10" type="range" class="form-control-range" [(ngModel)]="scene.lightning.intensity" (input)="scene.changeLightningIntensity()" >
              </div>

          </div>

          <!--Surface Inclanation Scan-->
          <div class="card" *ngIf="!ui.is2D()" >

            <div class="checkbox-label flex-between" [ngClass]="surfaceInclanation.step ? 'bg-mark' : ''" (click)="clearActions('surface_inclanation'); surfaceInclanation.toggle();" >
              <span class="mr-2" >Dieptescan</span>
              <span><i class="fa-solid fa-chart-area"></i></span>
            </div>

            <div *ngIf="!surfaceInclanation.loading" >

              <!--Select-->
              <div *ngIf="surfaceInclanation.step === 'select'" >

                <div class="my-2" >
                  <div class="text-center text-muted" >Selecteer het gebied</div>
                </div>

              </div>

              <!--Confirm-->
              <div *ngIf="surfaceInclanation.step === 'confirm'"  >

                <!--Precision-->
                <div class="my-2">
                  <div>Scan nauwkeurigheid</div>
                  <select class="form-control" [(ngModel)]="surfaceInclanation.pixels_per_meter" (change)="surfaceInclanation.calculateCellsDimensions()" >
                    <option value="10" *ngIf="auth.user?.is_developer" >Developer's Option</option>
                    <option value="100" >Heel laag</option>
                    <option value="400" >Laag</option>
                    <option value="2500" >Gemiddeld</option>
                    <option value="10000" >Hoog</option>
                    <option value="40000" >Heel hoog</option>
                  </select>
                </div>

                <!--Scan Info-->
                <div class="my-2"  >
                  <div class="flex-between" >
                    <span>Oppervlakte:</span>
                    <span>{{surfaceInclanation.surface_area}}m²</span>
                  </div>
                  <div class="flex-between" >
                    <span>Scan Resolutie:</span>
                    <span>{{surfaceInclanation.columns}}x{{surfaceInclanation.rows}}</span>
                  </div>
                  <div class="flex-between" >
                    <span>Aantal metingen:</span>
                    <span>{{surfaceInclanation.total_pixels}}</span>
                  </div>
                </div>

                <div class="mb-2 mt-4 text-center flex-align" >
                  <a class="btn btn-sm btn-inverse-success rounded-pill mr-1 w-75" (click)="surfaceInclanation.run()" >Starten</a>
                  <a class="btn btn-sm btn-inverse-danger rounded-pill ml-1 w-25" (click)="surfaceInclanation.selectArea()" > <i class="fa-solid fa-rotate-right"></i> </a>
                </div>

                <div *ngIf="surfaceInclanation.total_pixels > 500000" class="font-size-07 text-danger text-center" >
                  Veel metingen kunnen crashes veroorzaken bij onvoldoende hardware resources.
                </div>

              </div>

            </div>

            <div *ngIf="surfaceInclanation.loading" class="text-center my-2" >
              <div class="spinner-border spinner-border-md text-warning" >
                <span class="sr-only"></span>
              </div>
            </div>

          </div>

          <!--Store metadata-->
          <div class="card" *ngIf="auth.authenticated && auth.model_manageable" >

              <div class="checkbox-label flex-between" (click)="metadata.store()" >
                  <span class="mr-2" >Metadata opslaan</span>
                  <span><i class="fa-regular fa-save"></i></span>
              </div>

          </div>

          <!--Refresh metadata-->
          <div class="card" *ngIf="!ui.is2D()" >
              <div class="checkbox-label flex-between" (click)="metadata.load()" >
                  <span class="mr-2" >Positie Herstellen</span>
                  <span><i class="fa-solid fa-up-down-left-right"></i></span>
              </div>
          </div>

      </div>

      <!--Mobile Controls-->
      <div class="position-fixed top-0 right-0 m-3" *ngIf="ui.rendering && helpers.isMobile() && ui.perspective !== 'FIRST_PERSON'" >
          <div class="card font-size-125">

              <div class="checkbox-label text-center px-3" [ngClass]="scene.controls.touches.ONE === 0 ? 'bg-mark' : ''" (click)="scene.setTouchesOne(0)" >
                  <i class="fa-solid fa-rotate m-0"></i>
              </div>
              <div class="checkbox-label text-center px-3" [ngClass]="scene.controls.touches.ONE === 1 ? 'bg-mark' : ''" (click)="scene.setTouchesOne(1)" >
                  <i class="fa-solid fa-up-down-left-right m-0"></i>
              </div>
              <div class="checkbox-label text-center px-3" [ngClass]="scene.controls.touches.ONE === 'ZOOM' ? 'bg-mark' : ''" (click)="scene.setTouchesOne('ZOOM')" >
                  <i class="fa-solid fa-magnifying-glass-plus m-0"></i>
              </div>

          </div>
      </div>

      <!--Debug-->
      <div class="position-fixed top-0 left-275-px m-3" *ngIf="auth.user?.is_developer && ui.rendering && !helpers.isMobile()"  >

        <!--Visualize-->
        <div class="card debug-card">
          <div class="flex-align checkbox-label font-size-075" (click)="debug.toggleTargetVisibility()" >
            <div class="dot-glow dot-glow-sm mr-2" [ngClass]="debug.target_visible ? 'dot-glow-success' : 'dot-glow-danger'" ></div>
            <div>Target weergeven</div>
          </div>
        </div>

        <!--Positions-->
        <div class="card debug-card">
          <div class="my-2">
            <div>Camera</div>
            <div class="form-control flex-align  font-size-075">
              <span>X</span>
              <span class="form-control-divider mx-2" ></span>
              <input class="form-control-plaintext font-size-075" [(ngModel)]="scene.camera.position.x" >
            </div>
            <div class="form-control flex-align  font-size-075">
              <span>Y</span>
              <span class="form-control-divider mx-2" ></span>
              <input class="form-control-plaintext font-size-075" [(ngModel)]="scene.camera.position.y" >
            </div>
            <div class="form-control flex-align  font-size-075">
              <span>Z</span>
              <span class="form-control-divider mx-2" ></span>
              <input class="form-control-plaintext font-size-075" [(ngModel)]="scene.camera.position.z" >
            </div>
          </div>
        </div>
        <div class="card debug-card">
          <div class="my-2">
            <div>Target</div>
            <div class="form-control flex-align  font-size-075">
              <span>X</span>
              <span class="form-control-divider mx-2" ></span>
              <input class="form-control-plaintext font-size-075" [(ngModel)]="scene.controls.target.x" >
            </div>
            <div class="form-control flex-align  font-size-075">
              <span>Y</span>
              <span class="form-control-divider mx-2" ></span>
              <input class="form-control-plaintext font-size-075" [(ngModel)]="scene.controls.target.y" >
            </div>
            <div class="form-control flex-align  font-size-075">
              <span>Z</span>
              <span class="form-control-divider mx-2" ></span>
              <input class="form-control-plaintext font-size-075" [(ngModel)]="scene.controls.target.z" >
            </div>
          </div>
        </div>

      </div>

      <!--Logos-->
      <div class="position-fixed bottom-0 right-0 m-3" *ngIf="!this.helpers.isMobile()" >
        <div class="flex-align">
          <a class="logo-conatiner" href="http://infordb.com" target="_blank" ><img src="/img/infordb_logo_white.png" height="20" ></a>
          <a class="logo-conatiner" href="http://dronewithamission.nl" target="_blank" ><img src="/img/logo_white.png" height="75" ></a>
        </div>
      </div>

      <!--Popups: Preview-->
      <div class="popup-backdrop"
           [class.active]="preview.state"
           (click)="preview.togglePreview()"
           (mouseup)="preview.onMouseUp($event)"
      ></div>
      <div class="position-fixed transition-3 rounded-10 ease-in-out"
           *ngIf="ui.rendering && preview.available && preview.closest_image"
           [ngStyle]="{
             'width': ui.toggles.preview_size ? preview.closest_image.fullscreen().width + 'px' : '250px',
             'height': ui.toggles.preview_size ? preview.closest_image.fullscreen().height + 'px' : '200px'
           }"
           [ngClass]="ui.toggles.preview_position ? 'bottom-50 left-50 translate-middle-from-bottom image-fullscreen z-index-99' : 'bottom-0 left-0 m-3'"
           (mouseup)="preview.onMouseUp($event)"
      >

        <div class="card m-0 h-100">
          <div class="flex-between">
            <div class="nobr overflow-hidden" >
              <span>Preview</span>
              <span class="mx-2 font-size-07 mx-2 text-muted" *ngIf="preview.state && !ui.toggles.preview_img" >Scroll om in/uit te zoomen. Klik en sleep om het beeld te verplaatsen.</span>
            </div>

            <!--Preview Active-->
            <div class="flex-align font-size-08 my-1" *ngIf="preview.state" >
              <a class="btn btn-sm py-0 text-white"  (click)="preview.togglePreview()" >
                <i class="fa-solid fa-xmark"></i>
              </a>
            </div>

            <!--Preview Inactive-->
            <div class="flex-align font-size-08" *ngIf="!preview.state" >
              <a class="btn btn-sm py-0"
                 (click)="preview.toggleMarkers()"
                 [ngClass]="preview.markers_state ? 'text-highlight' : ''"
              >
                <i class="fa-solid fa-location-dot"></i>
              </a>
            </div>

          </div>
          <div class="h-100 rounded-5 overflow-hidden">
            <div class="h-100 overflow-hidden background-center position-relative"
                 [class.preview-popup-container]="!preview.state"
                 (click)="!preview.state && preview.togglePreview()"
                 [hidden]="ui.toggles.preview_img"
            >
              <img
                [src]="preview.state ? preview.closest_image.url() : preview.closest_image.thumbnail()"
                [ngStyle]="preview.image_style"
                draggable="false"
                (dragstart)="false"
                (wheel)="preview.onZoom($event)"
                (mousedown)="preview.onMouseDown($event)"
                (mousemove)="preview.onMouseMove($event)"
                class="preview-popup-image"
              >

              <div class="bg-card-section bottom-0 d-flex flex-column m-3 p-2 position-absolute right-0 rounded-7" *ngIf="preview.state" >
                <a class="btn btn-sm btn-white mb-1" (click)="preview.zoomIn()" ><i class="fa-solid fa-magnifying-glass-plus"></i></a>
                <a class="btn btn-sm btn-white mt-1" (click)="preview.zoomOut()" [class.disabled]="preview.zoom.scale === 1" ><i class="fa-solid fa-magnifying-glass-minus"></i></a>
              </div>


            </div>
            <div [hidden]="!ui.toggles.preview_img" class="h-100 flex-center bg-black">
              <div class="loader"></div>
            </div>
          </div>
        </div>
      </div>

      <!--Popups: Thermal iframe-->
      <div class="popup-backdrop"
           [class.active]="thermal.state"
           (click)="thermal.toggle()"
      ></div>
      <div class="position-fixed transition-3 ease-in-out rounded-10 bottom-0 left-0"
           *ngIf="ui.rendering && thermal.available"
           [ngStyle]="{
             'width': ui.toggles.thermal_size ? '80vw' : '356px',
             'height': ui.toggles.thermal_size ? '80vh' : '200px',
             'left': ui.toggles.thermal_position ? '50%' : (preview.available ? 'calc(200px + 4rem)' : '0')
           }"
           [ngClass]="ui.toggles.thermal_position ? 'bottom-50 translate-middle-from-bottom image-fullscreen z-index-99' : 'bottom-0 m-3'"
      >
        <div class="card h-100 m-0">
          <div class="flex-between">
            <span>Thermisch model</span>

            <div class="flex-align font-size-08" *ngIf="thermal.state" >
              <a class="btn btn-sm py-0"
                 (click)="thermal.toggle()" >
                <i class="fa-solid fa-xmark"></i>
              </a>
            </div>


          </div>
          <div class="overflow-hidden rounded-5 bg-black h-100">
            <div class="overflow-hidden h-100 preview-popup-container"
              [class.preview-popup-container]="!thermal.state"
              (click)="!thermal.state && thermal.toggle()"
            >
              <iframe
                [hidden]="ui.toggles.thermal_iframe"
                [class.pointer-event-none]="!thermal.state"
                class="w-100 h-100" [src]="thermal.iframe_src" frameborder="0"
              ></iframe>
              <div [hidden]="!ui.toggles.thermal_iframe" class="flex-center w-100 h-100 overflow-hidden"><div class="loader"></div></div>
            </div>
          </div>
        </div>
      </div>

      <!--Address Timeline-->
      <div class="position-fixed bottom-0 left-50 translate-middle-x transition-2 timeline-container z-index-9" [ngStyle]="{'bottom': ui.toggles.timeline ? '0' : '-150px' }" *ngIf="ui.rendering && models.model" >


        <div class="flex-center mb-2">

          <div class="card font-size-085 p-1 rounded-pill w-px-400">
            <div class="flex-between checkbox-label rounded-pill" (click)="ui.toggles.timeline = !ui.toggles.timeline" >
              <span >Adres Tijdlijn</span>
              <span class="mx-1" ><i class="fa-solid fa-bars"></i></span>
            </div>
          </div>

        </div>

        <div class="card"  >

          <div class="overflow-auto scroll">
            <div class="flex-center mx--1">
              <div *ngFor="let address_model of models.address_models" class="address-model-preview mx-1" [ngClass]="address_model.id == models.model.id ? 'active' : ''" (click)="models.openModel(address_model.guid)" >
                <div class="font-size-075 text-muted flex-between" >
                  <span class="max-w-175 nobr overflow-hidden" >{{address_model.name}}</span>
                  <span class="max-w-100 nobr overflow-hidden" >{{helpers.parseDate(address_model.created_at).day_full}} {{helpers.parseDate(address_model.created_at).month_short_name}} {{helpers.parseDate(address_model.created_at).year}}</span>
                </div>
                <div class="h-px-100 overflow-hidden" >
                  <img [src]="address_model.getMetaValue('preview_img')" class="h-100">
                </div>
              </div>
            </div>
          </div>

        </div>

      </div>

      <!--Tutorial-->
      <div class="position-fixed top-0 left-0 w-100 h-100 flex-center tutorial-modal" *ngIf="ui.rendering && tutorial.state && !helpers.isMobile() && !ui.is2D()" >

          <div class="card w-50 h-33" >

              <div class="position-absolute top-0 right-0 p-3" >
                  <a class="btn card-title opacity-66 opacity-hover-1" (click)="tutorial.close()" > <i class="fa-solid fa-xmark"></i> </a>
              </div>

              <div *ngIf="tutorial.page === 1" >
                  <div class="card-header card-title">Welkom in de omgeving van Drone Missions!</div>
                  <div class="card-body scroll">
                      <p>In deze wizard leiden we je stap voor stap door de basisfuncties en mogelijkheden van onze omgeving, zodat je het meeste uit je ervaring kunt halen. Hier is een overzicht van wat je kunt verwachten:</p>
                      <ul>
                          <li><b>Kiezen van de weergavemodus:</b> Leer hoe je kunt schakelen tussen de Lite Mode en Full Mode, en ontdek welke modus het beste bij je apparaat en behoeften past.</li>
                          <li><b>Navigeren door 3D-modellen:</b> Ontvang tips over het efficiënt verkennen van je modellen.</li>
                          <li><b>Meten in 3D:</b> Begrijp hoe je nauwkeurige metingen kunt uitvoeren en welke voordelen dit biedt.</li>
                      </ul>
                      <p>Klik op 'Volgende' om te beginnen met de wizard en ontdek alles wat onze omgeving te bieden heeft.</p>
                      <p>We wensen je veel succes en plezier!</p>
                  </div>
              </div>
              <div *ngIf="tutorial.page === 2" >
                  <div class="card-header card-title">Linker Muisknop</div>
                  <div class="card-body scroll">
                      <div class="d-flex">
                          <div class="w-100 pr-2">

                              <p>Met de linker muisknop kun je vrij rond het 3D-model draaien. Dit helpt je om het model vanuit alle hoeken te bekijken en te inspecteren.</p>
                              <ul>
                                  <li> <b>Rondom draaien: </b> Houd de linker muisknop ingedrukt en beweeg de muis om het model in verschillende richtingen te draaien.</li>
                                  <li> <b>Detailinspectie:</b> Draai het model om specifieke details vanuit verschillende perspectieven te bekijken.</li>
                              </ul>
                              <p>Gebruik deze functie om een volledig begrip te krijgen van de vorm en structuur van je 3D-model.</p>
                              <p>Klik op 'Volgende' om verder te gaan en meer te leren over andere navigatiefuncties.</p>

                          </div>
                          <div>
                              <img src="/img/tutorial/left_mouse.gif" class="gif" >
                          </div>
                      </div>
                  </div>
              </div>
              <div *ngIf="tutorial.page === 3" >
                  <div class="card-header card-title">Rechter Muisknop</div>
                  <div class="card-body scroll">
                      <div class="d-flex">
                          <div class="w-100 pr-2">

                              <p>Met de rechter muisknop kun je de positie van de camera bepalen, waardoor je de focus van je weergave aanpast.</p>
                              <ul>
                                  <li><b>Camera verplaatsen:</b> Houd de rechter muisknop ingedrukt en beweeg de muis om de camera te verplaatsen. Dit stelt je in staat om het model vanuit verschillende standpunten te bekijken.</li>
                              </ul>
                              <p>Gebruik deze functie om flexibel door het model te navigeren en specifieke delen van het model in detail te bekijken.</p>
                              <p>Klik op 'Volgende' om verder te gaan en meer te leren over andere navigatiefuncties.</p>

                          </div>
                          <div>
                              <img src="/img/tutorial/right_mouse.gif" class="gif" >
                          </div>
                      </div>
                  </div>
              </div>
              <div *ngIf="tutorial.page === 4">
                  <div class="card-header card-title">Scrollwiel Indrukken</div>
                  <div class="card-body scroll">
                      <div class="d-flex">
                          <div class="w-100 pr-2">

                              <p>Door het scrollwiel van je muis in te drukken, kun je snel het focuspunt van de camera bepalen.</p>
                              <ul>
                                  <li><b>Focuspunt instellen:</b> Druk het scrollwiel in om het focuspunt direct op de plek van de cursor te leggen. Dit helpt je om snel te focussen op specifieke delen van het model.</li>
                                  <li><b>Automatisch draaien: </b> Let op dat het camerastandpunt kan draaien, omdat de camera altijd recht naar het nieuwe focuspunt wordt gericht. Dit kan de kijkhoek van je model veranderen.</li>
                              </ul>
                              <p>Gebruik deze functie om snel en nauwkeurig te navigeren naar de details die je wilt bekijken.</p>
                              <p>Klik op 'Volgende' om verder te gaan en meer te leren over andere navigatiefuncties.</p>

                          </div>
                          <div>
                              <img src="/img/tutorial/scroll_button.gif" class="gif">
                          </div>
                      </div>
                  </div>
              </div>
              <div *ngIf="tutorial.page === 5">
                  <div class="card-header card-title">Scrollen</div>
                  <div class="card-body scroll">
                      <div class="d-flex">
                          <div class="w-100 pr-2">

                              <p>Met de scrollfunctie van je muis kun je eenvoudig in- en uitzoomen op je 3D-model.</p>
                              <ul>
                                  <li><b>Inzoomen: </b> Rol het scrollwiel naar voren om in te zoomen op het model. Dit helpt je om meer details van dichtbij te bekijken.</li>
                                  <li><b>Uitzoomen: </b> Rol het scrollwiel naar achteren om uit te zoomen. Dit geeft je een breder overzicht van het gehele model.</li>
                              </ul>
                              <p>Gebruik deze functie om de gewenste afstand tot het model te bepalen en nauwkeurige inspecties of overzichtelijke weergaven te krijgen.</p>
                              <p>Klik op 'Volgende' om verder te gaan en meer te leren over andere navigatiefuncties.</p>

                          </div>
                          <div>
                              <img src="/img/tutorial/zoom.gif" class="gif">
                          </div>
                      </div>
                  </div>
              </div>
              <div *ngIf="tutorial.page === 6">
                  <div class="card-header card-title">Renderafstand</div>
                  <div class="card-body scroll">
                      <div class="d-flex">
                          <div class="w-100 pr-2">

                              <p>De renderafstand is een belangrijke functie die je helpt om de balans tussen kwaliteit en prestaties te beheren.</p>
                              <ul>
                                  <li><b>Schuifregelaar linksboven:</b> Gebruik de schuifregelaar linksboven om de renderafstand aan te passen.</li>
                                  <li><b>Kwaliteit vs. Snelheid:</b> Het gebied waar de camera op richt, wordt in hoge kwaliteit weergegeven, terwijl de rest van de omgeving in lagere kwaliteit blijft om de navigatiesnelheid te bevorderen.</li>
                                  <li><b>Aanpassen renderafstand:</b> Door de renderafstand te vergroten, wordt een groter deel van het model in hoge kwaliteit ingeladen. Door deze te verkleinen, beperk je dit tot een kleiner gebied, wat kan helpen bij het verbeteren van de prestaties.</li>
                                  <li><b>Geen functie in Lite Mode:</b> In Lite Mode is deze functie niet beschikbaar omdat alles in lage kwaliteit wordt ingeladen om de beste prestaties op langzamere apparaten te garanderen.</li>
                              </ul>
                              <p>Gebruik deze schuifregelaar om je navigatie-ervaring te optimaliseren op basis van je apparaat en behoeften.</p>
                              <p>Klik op 'Volgende' om verder te gaan en meer te leren over andere navigatiefuncties.</p>

                          </div>
                          <div>
                              <img src="/img/tutorial/render_distance.gif" class="gif">
                          </div>
                      </div>
                  </div>
              </div>
              <div *ngIf="tutorial.page === 7">
                  <div class="card-header card-title">Meten</div>
                  <div class="card-body scroll">
                      <div class="d-flex">
                          <div class="w-100 pr-2">

                              <p>Met de meetfunctie kun je nauwkeurig afstanden en oppervlakten in je 3D-model bepalen.</p>
                              <ul>
                                  <li><b>Punten plaatsen:</b> Klik op het model om meetpunten te plaatsen. De tussenliggende afstanden tussen de punten worden direct weergegeven, evenals de oppervlakten van de gevormde gebieden.</li>
                                  <li><b>Resultaten bekijken:</b> Terwijl je punten toevoegt, zie je automatisch de gemeten afstanden en oppervlakten, waardoor je snel en efficiënt metingen kunt uitvoeren.</li>
                                  <li><b>Punten verwijderen:</b> Klik met de rechter muisknop op een punt om het te verwijderen en de metingen aan te passen.</li>
                              </ul>
                              <p>Gebruik deze functie om nauwkeurige metingen in je 3D-model uit te voeren en direct inzicht te krijgen in afstanden en oppervlakten.</p>

                          </div>
                          <div>
                              <img src="/img/tutorial/measure.gif" class="gif">
                          </div>
                      </div>
                  </div>
              </div>


              <div class="card-footer flex-end mt-auto">
                  <a class="btn btn-sm btn-outline-highlight mx-1" *ngIf="tutorial.page > 1" (click)="tutorial.previousPage()" >Vorige</a>
                  <a class="btn btn-sm btn-highlight mx-1 rounded-4" (click)="tutorial.nextPage()" *ngIf="tutorial.page < tutorial.pages" >Volgende</a>
                  <a class="btn btn-sm btn-highlight mx-1 rounded-4" (click)="tutorial.close()" *ngIf="tutorial.page == tutorial.pages">Afsluiten</a>
              </div>

          </div>

      </div>

      <!--Mode select-->
      <div *ngIf="!ui.mode && models.model" class="top-0 left-0 w-100 h-100 position-fixed d-flex flex-column align-items-center bg-black overflow-auto p-3 mode-section" >
          <div class="container">
              <div class="row" >
                  <div class="col-12 text-center my-4" >
                      <img src="/img/logo_muted.png" style="width: 50%; max-width: 300px" >
                      <div class="font-size-125" >Welkom bij de omgeving van Drone Missions!</div>
                      <div class="text-muted" >Kies hieronder de gewenste weergavemodus voor uw 3D-modellen:</div>
                  </div>

                  <!--Mobile modes-->
                  <div class="col-md col-12 position-relative" *ngIf="helpers.isMobile()" >
                    <div class="position-absolute w-100 h-100 disabled-mode" *ngIf="!models.model._2D_mobile" ></div>
                    <a class="d-block btn btn-outline-highlight w-100 my-3 rounded-2 font-size-15 nobr" (click)="ui.setMode('2D MOBILE')" >2D MOBILE MODE</a>
                    <div class="my-3" >
                      <img src="/img/tutorial/2D_full.png" class="w-100 rounded-4" >
                    </div>
                    <div class="text-muted font-size-08" >
                      <div>De 2D Mobile Mode is geoptimaliseerd voor mobiele apparaten en biedt een eenvoudige, laag-resolutie weergave van uw project. Deze modus is ideaal voor snel overzicht en navigatie onderweg. Metingen zijn in deze modus  niet beschikbaar.</div>
                    </div>
                  </div>
                  <div class="col-md col-12 position-relative" *ngIf="helpers.isMobile()" >
                    <div class="position-absolute w-100 h-100 disabled-mode" *ngIf="!models.model._mobile" ></div>
                    <a class="d-block btn btn-outline-highlight w-100 my-3 rounded-2 font-size-15 nobr" (click)="ui.setMode('MOBILE')" >3D MOBILE MODE</a>
                    <div class="my-3" >
                      <img src="/img/tutorial/mobile_mode.png" class="w-100 rounded-4" >
                    </div>
                    <div class="text-muted font-size-08" >
                      <div>De 3D Mobile Mode is speciaal geoptimaliseerd voor mobiele apparaten en biedt de mogelijkheid om uw 3D-model in een lagere resolutie te bekijken. Hoewel de resolutie lager is dan in de Desktop Mode, blijft de Mobile Mode ideaal voor het snel bekijken van uw modellen onderweg. Metingen zijn in deze modus niet beschikbaar.</div>
                    </div>
                  </div>

                  <!--Desktop 2D-->
                  <div class="col-md col-12 position-relative" *ngIf="!helpers.isMobile()" >
                    <div class="position-absolute w-100 h-100 disabled-mode" *ngIf="!models.model._2D_lite" ></div>
                    <a class="d-block btn btn-outline-highlight w-100 my-3 rounded-2 font-size-15 nobr" (click)="ui.setMode('2D LITE')" >2D LITE MODE</a>
                    <div class="my-3" >
                      <img src="/img/tutorial/2D_lite.png" class="w-100 rounded-4" >
                    </div>
                    <div class="text-muted font-size-08" >
                      <div>In de 2D Lite Mode wordt een vereenvoudigde weergave van uw objecten getoond, wat zorgt voor snellere navigatie op apparaten met beperkte rekenkracht. Deze modus is ideaal voor snel bladeren door uw project, maar metingen zijn minder nauwkeurig dan in de Full Mode.</div>
                    </div>
                  </div>
                  <div class="col-md col-12 position-relative" *ngIf="!helpers.isMobile()" >
                    <div class="position-absolute w-100 h-100 disabled-mode" *ngIf="!models.model._2D_full" ></div>
                    <a class="d-block btn btn-highlight w-100 my-3 rounded-2 font-size-15 nobr" (click)="ui.setMode('2D FULL')" >2D FULL MODE</a>
                    <div class="my-3" >
                      <img src="/img/tutorial/2D_full.png" class="w-100 rounded-4" >
                    </div>
                    <div class="text-muted font-size-08" >
                      <div>In de 2D Full Mode kunt u uw objecten vanuit een bovenaanzicht bekijken, ideaal voor het nauwkeurig meten van afstanden. Deze modus biedt een efficiënte manier om snel door uw project te navigeren, met behoud van snelheid en nauwkeurigheid, maar zonder diepte-informatie.</div>
                    </div>
                  </div>

                  <!--Desktop 3D-->
                  <div class="col-md col-12 position-relative" *ngIf="!helpers.isMobile()" >
                      <div class="position-absolute w-100 h-100 disabled-mode" *ngIf="!models.model._lite" ></div>
                      <a class="d-block btn btn-outline-highlight w-100 my-3 rounded-2 font-size-15 nobr" (click)="ui.setMode('LITE')" >3D LITE MODE</a>
                      <div class="my-3" >
                          <img src="/img/tutorial/lite_mode.png" class="w-100 rounded-4" >
                      </div>
                      <div class="text-muted font-size-08" >
                          <div>In de Lite Mode wordt enkel het lage kwaliteitsmodel van uw 3D-object ingeladen. Deze modus is ideaal voor langzamere pc's, omdat het minder rekenkracht vereist en soepeler zal werken. We raden de Lite Mode aan als u snel en efficiënt door uw modellen wilt navigeren zonder uw apparaat te belasten. Houd er rekening mee dat metingen in een 3D-model in deze modus minder nauwkeurig kunnen zijn.</div>
                      </div>
                  </div>
                  <div class="col-md col-12 position-relative" *ngIf="!helpers.isMobile()" >
                      <div class="position-absolute w-100 h-100 disabled-mode" *ngIf="!models.model._full" ></div>
                      <a class="d-block btn btn-highlight w-100 my-3 rounded-2 font-size-15 nobr" (click)="ui.setMode('FULL')" >3D FULL MODE</a>
                      <div class="my-3" >
                          <img src="/img/tutorial/full_mode.png" class="w-100 rounded-4" >
                      </div>
                      <div class="text-muted font-size-08" >
                          <div>In de Full Mode worden zowel het hoge als het lage kwaliteitsmodel van uw 3D-object ingeladen. Afhankelijk van de camerapositie schakelt de software automatisch tussen het hoge en lage kwaliteitsmodel voor een optimale balans tussen detail en prestaties. Deze modus biedt de beste visuele ervaring en wordt aanbevolen voor apparaten met voldoende rekenkracht. Het meten in een 3D-model is in Full Mode nauwkeuriger dan in de Lite Mode.</div>
                      </div>
                  </div>

                  <div *ngIf="helpers.isMobile() && !helpers.scrolledToBottom('.mode-section')" class="position-fixed bottom-0 left-50 translate-middle-x mb-3" >
                    <img src="img/scroll_down.gif" class="opacity-50" width="100">
                  </div>

              </div>
          </div>
      </div>

      <!--Modals-->
      <section >

        <!--Mode-->
        <div class="modal fade" [ngClass]="ui.modals.mode ? 'show d-block bg-fade' : ''" (click)="ui.dismissAskSetMode()" >
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <div class="py-2" >Weet je zeker dat je naar <b>{{ui.modals.mode}} MODE</b> wilt switchen?</div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn text-danger" (click)="ui.dismissAskSetMode()" >Annuleren</button>
                        <button type="button" class="btn btn-highlight" (click)="ui.setMode(ui.modals.mode)" >Bevestigen</button>
                    </div>
                </div>
            </div>
        </div>

        <!--Surface Inclanation-->
        <div class="modal fade"
             [ngClass]="(surfaceInclanation.step == 'generating' || surfaceInclanation.step == 'generated') ? 'show d-block bg-fade' : ''"
             (click)="surfaceInclanation.toggle()"
        >
          <div class="modal-dialog modal-lg modal-dialog-centered"  (click)="$event.stopPropagation()" >
            <div class="modal-content">
              <div class="modal-body">

                <div  class="surface-scan-header"
                  [class.active]="surfaceInclanation.step === 'generating'"
                >

                  <div class="surface-scan-badge" >
                    <div class="w-ch-5 text-center" >{{surfaceInclanation.render_duration}}</div>
                    <span class="text-muted mx-1" > / </span>
                    <div class="w-ch-5 text-center" >{{surfaceInclanation.render_complete_eta}}</div>
                  </div>

                  <div class="surface-scan-badge" >
                    <span class="mr-1" >Metingen: </span>
                    <div [style.width]="surfaceInclanation.total_pixels.toString().length + 'ch'" class="text-right" >{{surfaceInclanation.rendered_pixels}}</div>
                    <span class="text-muted mx-1" > / </span>
                    <div [style.width]="surfaceInclanation.total_pixels.toString().length + 'ch'" >{{surfaceInclanation.total_pixels}}</div>
                  </div>

                  <div class="surface-scan-badge flex-align" >
                    <div class="loading-bar-container">
                      <div class="loading-bar"
                           [style.width]="surfaceInclanation.render_percentage + '%'"
                      ></div>
                    </div>
                    <div class="w-ch-5 text-center" >{{surfaceInclanation.render_percentage}}%</div>

                  </div>

                </div>

                <div class="vh-66 flex-between" >
                  <div #surfaceInclanationCanvasContainer class="d-flex justify-content-end w-100 h-100 position-relative"
                       (mouseleave)="surfaceInclanation.onCanvasContainerLeave($event)"
                       (mousemove)="surfaceInclanation.onCanvasContainerHover($event)"
                  >
                    <span class="surface-scan-label ml-125px" >DIEPTEMAP</span>

                    <canvas  #surfaceInclanationCanvas
                             class="surface-scan-canvas"
                             [width]="surfaceInclanation.canvas_width"
                             [height]="surfaceInclanation.canvas_height"
                    >
                    </canvas>

                    <!--Charts-->
                    <div class="surface-scan-side-chart">
                      <span class="surface-scan-label surface-scan-label-vertical" >VER. DOORSNEDE</span>
                      <canvas #surfaceInclanationSideChartCanvas ></canvas>
                    </div>
                    <div class="surface-scan-bottom-chart">
                      <span class="surface-scan-label" >HOR. DOORSNEDE</span>
                      <canvas #surfaceInclanationBottomChartCanvas ></canvas>
                    </div>

                    <!--Preview-->
                    <div class="surface-scan-preview">
                      <span class="surface-scan-label" >MEETPUNT</span>
                      <div class="flex-center w-100" *ngIf="surfaceInclanation.preview_distnace !== null" >
                        <div class="surface-scan-preview-dot"
                             [style.background-color]="surfaceInclanation.preview_color"
                             style="box-shadow: 0 0 0 .25rem {{surfaceInclanation.preview_color}}33"
                        ></div>
                        <div>{{surfaceInclanation.preview_distnace}}</div>
                      </div>
                    </div>

                    <!--Indicators-->
                    <div *ngIf="surfaceInclanation.canvas_indicator" >
                      <div class="surface-scan-indicator-center"
                           [style.left]="surfaceInclanation.canvas_indicator_center_position.left"
                           [style.top]="surfaceInclanation.canvas_indicator_center_position.top"
                      ></div>

                      <div class="surface-scan-indicator-line surface-scan-indicator-horizontal"
                           [style.left]="surfaceInclanation.canvas_indicator_left_position.left"
                           [style.top]="surfaceInclanation.canvas_indicator_left_position.top"
                           [style.width]="surfaceInclanation.canvas_indicator_left_position.width"
                      >
                      </div>
                      <div class="surface-scan-indicator-line surface-scan-indicator-horizontal"
                           [style.left]="surfaceInclanation.canvas_indicator_right_position.left"
                           [style.top]="surfaceInclanation.canvas_indicator_right_position.top"
                           [style.width]="surfaceInclanation.canvas_indicator_right_position.width"
                      >
                      </div>
                      <div class="surface-scan-indicator-line surface-scan-indicator-vertical"
                           [style.left]="surfaceInclanation.canvas_indicator_top_position.left"
                           [style.top]="surfaceInclanation.canvas_indicator_top_position.top"
                           [style.height]="surfaceInclanation.canvas_indicator_top_position.height"
                      >
                      </div>
                      <div class="surface-scan-indicator-line surface-scan-indicator-vertical"
                           [style.left]="surfaceInclanation.canvas_indicator_bottom_position.left"
                           [style.top]="surfaceInclanation.canvas_indicator_bottom_position.top"
                           [style.height]="surfaceInclanation.canvas_indicator_bottom_position.height"
                      >
                      </div>

                    </div>

                  </div>

                  <div class="surface-scan-numbers-container" >
                    <span class="surface-scan-label surface-scan-label-vertical" >KLEURENSCHAAL</span>

                    <div class="surface-scan-scale" ></div>
                    <div class="h-100 py-1 hover-mark rounded-5 ml-2"
                      (wheel)="surfaceInclanation.setScale($event)"
                    >
                      <div class="surface-scan-scale-numbers" >
                        <div class="surface-scan-scale-number"
                             *ngFor="let number of surfaceInclanation.scale_numbers"
                             [style.top]="number.offset+'%'"
                        >
                          <span>- {{number.formatted_number}}</span>
                        </div>
                      </div>
                    </div>

                  </div>

                </div>

              </div>
            </div>
          </div>
        </div>

      </section>

      <!--Screen Selection-->
      <div class="screen-selector"
           *ngIf="screenSelection.state && screenSelection.startExists() && screenSelection.visible"
            [style.top]="screenSelection.top+'px'"
            [style.left]="screenSelection.left+'px'"
            [style.width]="screenSelection.width+'px'"
            [style.height]="screenSelection.height+'px'"
            [style.border-color]="screenSelection.border_color"
            [style.background-color]="screenSelection.background_color"
            [class.bg-unset]="screenSelection.transparent_background"
      ></div>

      <app-confirmation-modal
        [show]="ui.confirm_modal.show"
        [large]="ui.confirm_modal.large"
        [title]="ui.confirm_modal.title"
        [message]="ui.confirm_modal.message"
        [confirm_text]="ui.confirm_modal.confirm_text"
        [confirm_color]="ui.confirm_modal.confirm_color"
        [cancel_text]="ui.confirm_modal.cancel_text"
        (confirm)="ui.confirm_modal.onConfirm(); ui.hideConfirmModal()"
        (cancel)="ui.hideConfirmModal()"
      ></app-confirmation-modal>

    </div>

    <!--Loading-->
    <div *ngIf="ui.loading" >

      <div class="top-0 left-0 w-100 h-100 position-fixed flex-center" *ngIf="ui.loading" >
        <div class="loader"></div>
      </div>

      <div *ngIf="ui.show" >
        <div *ngIf="ui.messages.length" class="position-fixed top-0 right-0 m-3 nobr" >
          <div *ngFor="let message of ui.messages" >{{message.message}}</div>
        </div>
        <div *ngIf="ui.mode == 'FULL'" class="position-fixed top-0 left-0 m-3 nobr" >
          <a class="d-block btn btn-outline-highlight w-100 rounded-2 font-size-1 nobr" (click)="ui.setMode('LITE')" >LITE MODE</a>
        </div>
        <div *ngIf="ui.mode == '2D FULL'" class="position-fixed top-0 left-0 m-3 nobr" >
          <a class="d-block btn btn-outline-highlight w-100 rounded-2 font-size-1 nobr" (click)="ui.setMode('2D LITE')" >2D LITE MODE</a>
        </div>
      </div>
    </div>


</section>
