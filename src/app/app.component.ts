import { Component } from '@angular/core';
import {HelpersService} from "./services/helpers/helpers.service";
import {Title} from "@angular/platform-browser";
import {UiService} from "./services/ui/ui.service";

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent {
  title = 'Editor';

  constructor(
      protected helpers: HelpersService,
      protected uiService: UiService,
      protected titleService: Title,
  ) {

    if(this.helpers._get('title')){
      this.titleService.setTitle(this.helpers._get('title'));
    }

  }

}
