import {Component, OnInit} from '@angular/core';
import {ActivatedRoute} from "@angular/router";
import {HelpersService} from "../services/helpers/helpers.service";
import {AuthService} from "../services/auth/auth.service";

@Component({
    selector: 'app-login',
    templateUrl: './login.component.html',
    styleUrl: './login.component.css'
})
export class LoginComponent implements OnInit {

    constructor(
        private helpers: HelpersService,
        private auth: AuthService,
        private route: ActivatedRoute
    ) {
    }

    ngOnInit() {
        const query_params = new URLSearchParams( this.route.snapshot.queryParams ).toString();

        const token = this.route.snapshot.paramMap.get('token');
        const path = this.route.snapshot.paramMap.get('path');
        if (!token || !path) {
            return;
        }

        this.auth.login(token);
        window.location.href = `${path}?${query_params}`;
    }

}
