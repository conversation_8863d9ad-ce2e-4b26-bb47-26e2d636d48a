import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { EditorComponent } from './editor/editor.component';
import {FormsModule} from "@angular/forms";
import { LoginComponent } from './login/login.component';
import { DisplayComponent } from './display/display.component';
import {ColorPickerModule} from "ngx-color-picker";
import { ConfirmationModalComponent } from './components/confirmation-modal/confirmation-modal.component';

@NgModule({
  declarations: [
    AppComponent,
    EditorComponent,
    LoginComponent,
    DisplayComponent,
    ConfirmationModalComponent
  ],
    imports: [
        BrowserModule,
        AppRoutingModule,
        FormsModule,
        ColorPickerModule
    ],
  providers: [],
  bootstrap: [AppComponent]
})
export class AppModule { }
