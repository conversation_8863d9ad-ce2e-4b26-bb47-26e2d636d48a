<router-outlet />

<div class="corner-loader" *ngIf="uiService.rendering && uiService.show && (helpers.posting || helpers.notification.message)" >

    <div class="spinner-border text-warning" *ngIf="helpers.posting" >
        <span class="sr-only"></span>
    </div>

    <div class="corner-loader-content text-{{helpers.notification.color}}" *ngIf="helpers.notification.message" >
        {{helpers.notification.message}}
    </div>

</div>
