import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-confirmation-modal',
  templateUrl: './confirmation-modal.component.html',
  styleUrl: './confirmation-modal.component.css'
})
export class ConfirmationModalComponent {

  @Input() show: boolean = false;
  @Input() large: boolean = false;
  @Input() title?: string;
  @Input() message: string = '';
  @Input() confirm_text: string = 'Bevestigen';
  @Input() confirm_color: string = 'danger';
  @Input() cancel_text: string = '';
  @Output() confirm = new EventEmitter<void>();
  @Output() cancel = new EventEmitter<void>();

  onConfirm() {
    this.confirm.emit();
  }
  onCancel() {
    this.cancel.emit();
  }

}
