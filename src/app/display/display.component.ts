import {Component, ElementRef, HostListener, OnInit, ViewChild} from '@angular/core';

import {ActivatedRoute} from "@angular/router";


import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import {OrbitControls} from "three/examples/jsm/controls/OrbitControls.js";
import {HelpersService} from "../services/helpers/helpers.service";
import * as MeshOpt from "meshoptimizer";

@Component({
  selector: 'app-display',
  templateUrl: './display.component.html',
  styleUrl: './display.component.css'
})
export class DisplayComponent implements OnInit{

  @ViewChild('canvas', { static: true }) canvasRef!: ElementRef;

  private model: any;
  private object: any;

  private scene = new THREE.Scene();
  private renderer: any;
  private camera: any;
  private controls: any;
  private mouse = new THREE.Vector2();

  constructor(
      private route: ActivatedRoute,
      private helpers: HelpersService,
  ) {}

  ngOnInit() {
    this.model = this.route.snapshot.paramMap.get('model');
    if(!this.model){ return; }

    this.initEditor();
    window.addEventListener('message', this.onMessageReceived.bind(this));
  }

  async initEditor() {

    //Scene
    const canvas = this.canvasRef.nativeElement;

    this.renderer = new THREE.WebGLRenderer({canvas});
    this.renderer.setSize(window.innerWidth, window.innerHeight);

    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(1, 1, 1);

    //Light
    const ambient = new THREE.AmbientLight(0xffffff, 5);
    this.scene.add(ambient);

    //Directional
    const directional_left = new THREE.DirectionalLight(0xffffff, 5);
    directional_left.position.x = 10
    directional_left.position.z = -10
    this.scene.add(directional_left);

    const directional_right = new THREE.DirectionalLight(0xffffff, 5);
    directional_right.position.x = -10
    directional_right.position.z = 10
    this.scene.add(directional_right);


    //Controls
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.target.set(0, 0, 0);

    if(this.helpers._get('controls') !== 'true'){
      this.controls.enabled = false;
    }
    this.controls.update();

    //Grid
    if(this.helpers._get('grid') == 'true'){
      const grid = new THREE.GridHelper(20, 10);
      this.scene.add(grid);
    }


    await this.initModel();
    this.initParams();


    this.render();
  }
  initModel(){
    return new Promise(resolve => {

      const loader = new GLTFLoader();
      loader.setMeshoptDecoder(MeshOpt.MeshoptDecoder);

      loader.load(
          `display/${this.model}.glb`,
          (gltf: any) => {
            this.object = gltf.scene;
            this.scene.add(this.object);
            resolve(true);
          }
      );

    });
  }
  initParams(){
    this.camera.position.x = Number(this.helpers._get('camera_x', this.camera.position_x));
    this.camera.position.y = Number(this.helpers._get('camera_y', this.camera.position_y));
    this.camera.position.z = Number(this.helpers._get('camera_z', this.camera.position_z));

    this.object.position.x = Number(this.helpers._get('object_x', this.object.position_x));
    this.object.position.y = Number(this.helpers._get('object_y', this.object.position_y));
    this.object.position.z = Number(this.helpers._get('object_z', this.object.position_z));

    const scale = Number(this.helpers._get('object_scale', 1));
    this.object.scale.set(scale, scale, scale)
  }

  render() {
    this.controls.update();
    this.renderer.render(this.scene, this.camera);

    this.lookAtCursor();

    if(this.object){
      this.object.rotation.x += Number(this.helpers._get('render_object_ration_x', this.object.rotation.x));
      this.object.rotation.y += Number(this.helpers._get('render_object_ration_y', this.object.rotation.y));
      this.object.rotation.z += Number(this.helpers._get('render_object_ration_z', this.object.rotation.z));
    }

    requestAnimationFrame(this.render.bind(this));
  }

  //Render options
  lookAtCursor(){
    if(!this.object || this.helpers._get('look_at') != 'true'){ return; }

    const vector = new THREE.Vector3(this.mouse.x, this.mouse.y, 0);
    vector.unproject(this.camera);

    const direction = vector.sub(this.camera.position).normalize();
    const distance = -this.camera.position.z / direction.z;
    const pos = this.camera.position.clone().add(direction.multiplyScalar(distance));

    this.object.lookAt(pos);
  }

  //Listeners
  @HostListener('window:resize', [])
  onResize() {
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
  }

  //Listeners
  @HostListener('window:click', [])
  onClick() {

    console.log(this.camera.position);
    console.log(this.object.position);
    console.log(this.object.rotation);
  }

  onMessageReceived(event: MessageEvent) {
    if (event.data.type === 'MOUSE_POSITION') {
      const x = event.data.x * window.innerWidth;
      const y = event.data.y * window.innerHeight;

      this.mouse.x = (x / window.innerWidth) * 2 - 1;
      this.mouse.y = -(y / window.innerHeight) * 2 + 1;
    }
  }
}
