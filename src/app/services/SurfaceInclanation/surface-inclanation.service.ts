import {ElementRef, Injectable} from '@angular/core';
import {MeasurementsService} from "../measurements/measurements.service";
import * as THREE from 'three'
import {Mesh, Vector3} from "three";
import {ModelsService} from "../models/models.service";
import {SceneService} from "../scene/scene.service";
import {HelpersService} from "../helpers/helpers.service";
import {BaseIndicatorService} from "../BaseIndicator/base-indicator.service";
import {ScreenSelectionService} from "../ScreenSelection/screen-selection.service";
import {Decimal} from 'decimal.js';
import {UiService} from "../ui/ui.service";
import {ScreenSelectionV3} from "../../types/ScreenSelection.types";
import {
  Chart,
  LineController,
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Tooltip,
  Filler
} from 'chart.js';
import {CalculationsService} from "../calculations/calculations.service";

Chart.register(
  LineController,
  LineElement,
  PointElement,
  LinearScale,
  CategoryScale,
  Tooltip,
  Filler
);

@Injectable({
  providedIn: 'root'
})
export class SurfaceInclanationService{

  //Machine state
  public loading: boolean = false;
  public step: null | 'select' | 'confirm' | 'generating' | 'generated' = null;
  public pause_rendering: boolean = false;

  //Canvas
  public canvas: ElementRef<HTMLCanvasElement> | null = null;
  public canvas_container: ElementRef<HTMLCanvasElement> | null = null;
  public ctx: CanvasRenderingContext2D | null = null;
  public canvas_width = 600;
  public canvas_height = 600;

  //Canvas charts
  public side_chart: any = null;
  public bottom_chart: any = null;
  public side_chart_canvas: ElementRef<HTMLCanvasElement> | null = null;
  public bottom_chart_canvas: ElementRef<HTMLCanvasElement> | null = null;

  //Canvas Preview
  public preview_distnace: string | null = null;
  public preview_color: string | null = null;

  //Canvas indicators
  public canvas_indicator: boolean = false;
  public canvas_indicator_center_position: {left: string, top: string} = {left: '0px', top: '0px'};
  public canvas_indicator_left_position: {left: string, top: string, width: string} = {left: '0px', top: '0px', width: '0px'};
  public canvas_indicator_right_position: {left: string, top: string, width: string} = {left: '0px', top: '0px', width: '0px'};
  public canvas_indicator_top_position: {left: string, top: string, height: string} = {left: '0px', top: '0px', height: '0px'};
  public canvas_indicator_bottom_position: {left: string, top: string, height: string} = {left: '0px', top: '0px', height: '0px'};

  //Plane
  public scan_plane: THREE.Mesh | null = null;
  public scanning_plane: THREE.Mesh | null = null;

  //Image
  public pixel_distances: number[] = [];
  public image_data: ImageData | null = null;
  public pixels_per_meter = 2500;

  //Render info
  public total_pixels: number = 0;
  public rendered_pixels: number = 0;
  public render_started_at: number = 0;
  public render_percentage: number = 0;
  public render_duration: string = '00:00';
  public render_complete_eta: string = '00:00';

  //Scale
  public scale_min: number | null = null;
  public scale_max: number | null = null;
  public scale_range: number  = 0;
  public scale_numbers: {number: number, formatted_number: string, offset: number}[] = [];

  //Scan
  public scan_positions: Vector3[] = [];
  public scanning_positions: Vector3[] = []

  //Dots
  public dots: THREE.Mesh[] = [];

  //Variables
  public selection_color: string = '#fb8300';
  public surface_area: number = 0;
  public rows: number = 0;
  public columns: number = 0;

  constructor(
    private helpers: HelpersService,
    private sceneService: SceneService,
    private measurementsService: MeasurementsService,
    private calculationsService: CalculationsService,
    private modelsService: ModelsService,
    private screenSelectionService: ScreenSelectionService,
    private uiService: UiService,
  ) {}

  init(canvas: ElementRef<HTMLCanvasElement>, canvas_container: ElementRef<HTMLCanvasElement>, side_chart: ElementRef<HTMLCanvasElement>, bottom_chart: ElementRef<HTMLCanvasElement>){
    this.canvas = canvas;
    this.canvas_container = canvas_container;
    this.side_chart_canvas = side_chart
    this.bottom_chart_canvas = bottom_chart
    this.ctx = this.canvas.nativeElement.getContext('2d');
  }
  toggle(){
    if(!this.step){
      this.selectArea();
      return;
    }

    this.step = null;
    this.clear();
  }
  async setLoading(state: boolean){
    await this.helpers.sleep(10);
    this.loading = state;
    await this.helpers.sleep(10);
  }

  //Clear
  clear(){
    this.clearPlanes();
    this.clearDots()
    this.clearPositions();
    this.destroyCharts();
    this.clearCanvas()
    this.screenSelectionService.abort();

    this.rendered_pixels = 0;
    this.render_percentage = 0;
    this.canvas_indicator = false;
    this.pause_rendering = false;
    this.image_data = null

    this.uiService.setCss('cursor', 'default');
  }
  clearPlanes(){
    this.sceneService.remove(this.scan_plane);
    this.sceneService.remove(this.scanning_plane);

    this.scan_plane = null;
    this.scanning_plane = null;
  }
  clearDots(){
    this.dots.forEach((dot: THREE.Mesh) => {
      this.sceneService.remove(dot);
    })

    this.dots = [];
  }
  clearPositions(){
    this.scan_positions = [];
    this.scanning_positions = [];
  }

  async selectArea() {
    this.clearPlanes();
    this.step = 'select';

    this.screenSelectionService.visible = false;
    this.screenSelectionService.v3_callback = this.selectAreaCallback.bind(this);
    this.screenSelectionService.objects = this.modelsService.getModelObjects('low', {include_maps: false});

    const data = await this.screenSelectionService.selectV3();
    if(!this.step){ return; }

    await this.selectAreaCallback(data);

    if(!this.scan_plane){
      await this.selectArea();
      return
    }

    this.calculateCellsDimensions();
    this.step = 'confirm';
  }
  async createPlanes(data: ScreenSelectionV3): Promise<void>{
    const { top_left, top_right, bottom_left, bottom_right } = data;

    // Preprocess vertices
    const points: THREE.Vector3[] = [];
    if (top_left) points.push(top_left);
    if (top_right) points.push(top_right);
    if (bottom_left) points.push(bottom_left);
    if (bottom_right) points.push(bottom_right);

    if(points.length < 3){ return; }

    const [p0, p1, p2] = points;

    // Force all corners to 90 degrees by building orthogonal basis
    const x_dir = p1.clone().sub(p0).normalize();
    const raw_y_dir = p2.clone().sub(p0).normalize();

    const dot = x_dir.dot(raw_y_dir);
    const y_dir = raw_y_dir.sub(x_dir.clone().multiplyScalar(dot)).normalize();

    const width = p1.distanceTo(p0);
    const height = p2.distanceTo(p0);

    const p1_ortho = p0.clone().add(x_dir.clone().multiplyScalar(width));
    const p2_ortho = p0.clone().add(y_dir.clone().multiplyScalar(height));
    const p3 = p0.clone().add(x_dir.clone().multiplyScalar(width)).add(y_dir.clone().multiplyScalar(height));

    const vertices: THREE.Vector3[] = [p0, p1_ortho, p2_ortho, p2_ortho, p1_ortho, p3];
    const flat_vertices: number[] = vertices.flatMap(v => [v.x, v.y, v.z]);


    // Create offsetted scanning plane
    const offset = this.modelsService.getModelMaxDiagonalSize() * 2;
    const normal = new THREE.Vector3().crossVectors(x_dir, y_dir).normalize().negate();

    const offset_vertices = vertices.map(v => v.clone().add(normal.clone().multiplyScalar(offset)));
    const flat_offset_vertices = offset_vertices.flatMap(v => [v.x, v.y, v.z]);

    this.scan_plane =  this.helpers.createFromBuffer(flat_vertices, this.selection_color);
    this.scanning_plane = this.helpers.createFromBuffer(flat_offset_vertices, this.selection_color);
    this.surface_area = this.calculationsService.calculateSurfaceArea([p0, p1_ortho, p3, p2_ortho]);
  }
  async selectAreaCallback(data: ScreenSelectionV3){
    this.clearPlanes();

    await this.createPlanes(data);
    if(!this.scan_plane){ return; }

    this.sceneService.add(this.scan_plane)
    //For debugging
    // this.sceneService.add(this.scanning_plane);
  }


  calculateCellsDimensions(): void{
    if(!this.scan_plane){ return; }

    const geometry = this.scan_plane.geometry as THREE.BufferGeometry;
    const position = geometry.getAttribute('position');

    const p0 = new THREE.Vector3().fromBufferAttribute(position, 0);
    const p1 = new THREE.Vector3().fromBufferAttribute(position, 1);
    const p2 = new THREE.Vector3().fromBufferAttribute(position, 2);

    const width = p0.distanceTo(p1);
    const height = p0.distanceTo(p2);

    this.rows = Math.round(Math.sqrt(this.pixels_per_meter) * height);
    this.columns = Math.round(Math.sqrt(this.pixels_per_meter) * width);
    this.total_pixels = this.rows * this.columns;
  }

  //Render
  async run(){
    this.step = 'generating';
    this.pixel_distances = [];
    this.screenSelectionService.abort();

    if (!this.ctx || !this.canvas || !this.scan_plane || !this.scanning_plane){ return; }

    await this.helpers.sleep(250);


    this.setCanvas();
    this.calculateCellsDimensions();
    this.initCharts();
    this.resetCanvasPreview();

    const scanner_group = this.helpers.subdivideMeshIntoGrid(this.scanning_plane, this.rows, this.columns);
    const scan_pos = this.helpers.getObjectGeometryWorldCenter(this.scan_plane);
    const scanning_pos = this.helpers.getObjectGeometryWorldCenter(this.scanning_plane);
    const normal = new THREE.Vector3().subVectors(scan_pos, scanning_pos).normalize();

    this.render_started_at = (new Date()).getTime();
    for(const i in scanner_group.children){
      if(!this.step){ return; }

      const index = Number(i);

      const origin_child = scanner_group.children[index] as THREE.Mesh;

      if (!(origin_child instanceof THREE.Mesh)) { continue; }

      const origin = this.helpers.getObjectGeometryWorldCenter(origin_child);

      const ray = this.calculationsService.getRaycastFromOrigin(origin, normal, this.modelsService.getModelObjects(this.uiService.mode == 'FULL' ? 'high' : 'low'));
      const distance = (ray?.distance || 0);

      this.pixel_distances.push(distance);
      this.sceneService.remove(origin_child);

      this.rendered_pixels++;

      if(index % Math.max(this.columns, this.rows) === 0){
        await this.updateCanvas();
        this.calculateRenderCompleteEta();
        this.calculateRenderPercentage();
      }
    }

    this.sceneService.remove(scanner_group);

    await this.updateCanvas();
    this.step = 'generated';
    this.pause_rendering = true;
  }
  calculateRenderCompleteEta(){
    const time = (new Date()).getTime();
    const passed = time - this.render_started_at;
    const ms_per_pixel = passed / this.rendered_pixels;

    this.render_duration = this.helpers.msToTime(passed);
    this.render_complete_eta = this.helpers.msToTime(ms_per_pixel * this.total_pixels);
  }
  calculateRenderPercentage(){
    this.render_percentage = new Decimal(this.rendered_pixels).dividedBy(this.total_pixels).times(100).toDecimalPlaces(1).toNumber()
  }

  // Canvas
  setCanvas(){
    if(!this.canvas){ return; }

    this.canvas_height = this.rows;
    this.canvas_width = this.columns;
    this.canvas.nativeElement.width = this.canvas_width;
    this.canvas.nativeElement.height = this.canvas_height;
  }
  clearCanvas(){
    if(!this.canvas || !this.ctx){ return; }

    const { width, height } = this.canvas.nativeElement;
    this.ctx.clearRect(0, 0, width, height);
  }
  async updateCanvas(){
    this.defineScaleBar()
    this.defineScaleRange();
    this.defineScaleNumbers();
    this.defineImageData();
    await this.drawCanvas();
  }
  async drawCanvas(){
    if(!this.ctx || !this.scale_min || !this.scale_max || !this.image_data){ return; }

    for (let i = 0; i < this.pixel_distances.length; i++) {
      const distance = this.pixel_distances[i];
      const normalized = this.getNormalizedDistance(distance)
      this.setImageData(i * 4, normalized);
    }

    await this.helpers.sleep(150);
    this.ctx.putImageData(this.image_data, 0, 0);
  }
  calculateCanvasSize(): {width: number, height: number} | null{
    if(!this.canvas || !this.canvas_container){ return null; }

    let { width: bounding_width, height: bounding_height } = this.canvas.nativeElement.getBoundingClientRect();
    let { width: native_width, height: native_height } = this.canvas.nativeElement;
    let { width: container_width, height: container_height } = this.canvas_container.nativeElement.getBoundingClientRect();

    const container_ratio = (container_width - 120) / (container_height - 120);
    const canvas_ratio = native_width / native_height;

    const width_ratio = bounding_width / native_width
    const height_ratio = bounding_height / native_height;

    let width = canvas_ratio > container_ratio
      ? bounding_width
      : height_ratio * native_width;

    let height = canvas_ratio < container_ratio
      ? bounding_height
      : width_ratio * native_height;

    return {width, height};
  }
  calculateCanvasOnHoverCords(event: MouseEvent): {x: number, y: number} | null{
    if(!this.canvas || !this.canvas_container){ return null; }

    const size = this.calculateCanvasSize();
    if(!size){ return null; }

    const { width, height } = size;

    let { width: bounding_width, height: bounding_height, left, top } = this.canvas.nativeElement.getBoundingClientRect();
    let { width: native_width, height: native_height } = this.canvas.nativeElement;

    left = new Decimal(event.clientX).minus(left).toDecimalPlaces(2).toNumber();
    top = new Decimal(event.clientY).minus(top).toDecimalPlaces(2).toNumber();

    left = left - (bounding_width - width) / 2;
    top = top - (bounding_height - height) / 2;

    if(
      left < 0 || left > width ||
      top < 0 || top > height
    ){ return null }

    const y = Math.ceil(native_height / height * top);
    const x = Math.ceil(native_width / width * left);

    return { x, y };
  }

  //Canvas Charts
  initCharts(){
    if(!this.side_chart_canvas || !this.bottom_chart_canvas || !this.canvas){ return; }
    if(this.bottom_chart && this.side_chart){ return; }

    const size = this.calculateCanvasSize();
    if(!size){ return; }

    const ctx_side = this.side_chart_canvas.nativeElement.getContext('2d');
    const ctx_bottom = this.bottom_chart_canvas.nativeElement.getContext('2d');

    //Dynamic width = scale padding
    this.bottom_chart_canvas.nativeElement.width = size.width;
    this.bottom_chart_canvas.nativeElement.height = 100;

    this.side_chart_canvas.nativeElement.width = 100;
    this.side_chart_canvas.nativeElement.height = size.height;

    const config = {
      type: 'line',
      data: {
        labels: ['', '', ''],
        datasets: [{
          label: 'Values',
          data: [0, 0, 0],
          fill: true,
          borderColor: '#ffffff',
          tension: 0.4,
          pointRadius: 0,
          segment: {
            borderColor: (ctx: any) => {
              console.log(ctx);
              const value = ctx.p0.parsed.y;
              return `rgb(${value * 255}, 0, 0)`;
            }
          }
        }]
      },
      options: {
        plugins: {
          tooltip: {
            enabled: false,
            mode: 'index',
            intersect: false
          },
          legend: {
            display: false
          }
        },
        indexAxis: 'x',
        responsive: false,
        scales: {
          x: { display: false },
          y: { display: false }
        }
      }
    };

    this.bottom_chart = new Chart(ctx_bottom!, JSON.parse(JSON.stringify(config)));
    this.nullifyBottomChart();

    config.options.indexAxis = 'y';
    this.side_chart = new Chart(ctx_side!, JSON.parse(JSON.stringify(config)));
    this.nullifySideChart();
  }
  updateCharts(cords: {x: number, y: number}){
    this.initCharts();
    if(!this.canvas || !this.image_data || !this.bottom_chart || !this.side_chart){ return; }

    const {x, y } = cords;
    const { width, height } = this.canvas.nativeElement;

    //Dynamic segment creation for border color management
    const createSegment = (direction: 'horizontal' | 'vertical') => ({
      borderColor: (ctx: any) => {
        const value = direction === 'horizontal'
          ? (ctx.p0.parsed.y + ctx.p1.parsed.y) / 2
          : (ctx.p0.parsed.x + ctx.p1.parsed.x) / 2;

        const normalized = this.getNormalizedDistance(this.revertRelativeDistance( Math.abs(value) ));
        const { r, g, b } = this.getPixelColor(normalized);
        return `rgb(${r}, ${g}, ${b})`;
      }
    });

    //Horizontal Data
    let horizontal_index = (y - 1) * width
    const horizontal_data = this.pixel_distances.slice(horizontal_index, horizontal_index + width);

    this.bottom_chart.data.labels = Array(horizontal_data.length).fill('');
    this.bottom_chart.data.datasets[0].data = horizontal_data.map((distance: number) => -this.getRelativeDistance(distance));
    this.bottom_chart.data.datasets[0].segment = createSegment('horizontal')
    this.bottom_chart.update();

    //Vertical Data
    const vertical_data = [];
    for (let row = 0; row < height; row++) {
      const index = row * width + (x - 1);
      vertical_data.push(this.pixel_distances[index]);
    }

    this.side_chart.data.labels = Array(vertical_data.length).fill('');
    this.side_chart.data.datasets[0].data = vertical_data.map((distance: number) => -this.getRelativeDistance(distance));
    this.side_chart.data.datasets[0].segment = createSegment('vertical')
    this.side_chart.update();
  }
  destroyCharts(){
    if(this.bottom_chart){
      this.bottom_chart.destroy();
      this.bottom_chart = null;
    }
    if(this.side_chart){
      this.side_chart.destroy();
      this.side_chart = null;
    }
  }
  nullifyBottomChart(){
    if(!this.bottom_chart){ return; }

    this.bottom_chart.data.labels = Array(this.columns).fill('');
    this.bottom_chart.data.datasets[0].data = Array(this.columns).fill(0);
    this.bottom_chart.update();
  }
  nullifySideChart(){
    if(!this.side_chart){ return; }

    this.side_chart.data.labels = Array(this.rows).fill('');
    this.side_chart.data.datasets[0].data = Array(this.rows).fill(0);
    this.side_chart.update();
  }

  //Canvas Indicators
  updateCanvasIndicators(event: MouseEvent): void{
    if(!this.canvas_container){ return; }

    const cords = this.calculateCanvasOnHoverCords(event);
    if(!cords){
      this.canvas_indicator = false;
      this.uiService.setCss('cursor', 'default');
      return;
    }

    const size = this.calculateCanvasSize();
    if(!size){ return }

    const { width, height } = size;
    let { width: container_width, height: container_height, left, top } = this.canvas_container.nativeElement.getBoundingClientRect();

    left = new Decimal(event.clientX).minus(left).toDecimalPlaces(2).toNumber();
    top = new Decimal(event.clientY).minus(top).toDecimalPlaces(2).toNumber();

    // Center indicator
    this.canvas_indicator_center_position.left = `${left}px`;
    this.canvas_indicator_center_position.top = `${top}px`;


    //Line indicators
    const padding_offset = (115 / 2) //CSS Canvas Margins
    const horizontal_width_diff = ((container_width - width) / 2)
    const vertical_height_diff = ((container_height - height) / 2)

    this.canvas_indicator_left_position = {
      left: `${horizontal_width_diff + padding_offset + 3}px`,
      top: `${top}px`,
      width: `${left - horizontal_width_diff - padding_offset - 10}px`
    }
    this.canvas_indicator_right_position = {
      left: `${left + 7}px`,
      top: `${top}px`,
      width: `${container_width - left - horizontal_width_diff + padding_offset - 5}px`
    };
    this.canvas_indicator_top_position = {
      left: `${left}px`,
      top: `${vertical_height_diff - padding_offset - 3}px`,
      height: `${top - vertical_height_diff + padding_offset - 5}px`
    };
    this.canvas_indicator_bottom_position = {
      left: `${left}px`,
      top: `${top + 8}px`,
      height: `${container_height - top - vertical_height_diff - padding_offset - 10}px`
    };

    this.canvas_indicator = true;
    this.uiService.setCss('cursor', 'none');
  }

  //Canvas listeners
  onCanvasContainerLeave(event: MouseEvent){

    if(this.step !== 'generated'){ return; }

    this.nullifySideChart();
    this.nullifyBottomChart();
    this.resetCanvasPreview();

    this.canvas_indicator = false;
    this.uiService.setCss('cursor', 'default');
  }
  onCanvasContainerHover(event: MouseEvent){
    if(this.step !== 'generated'){ return; }

    this.updateCanvasIndicators(event);

    const cords = this.calculateCanvasOnHoverCords(event);
    if(!cords){ return; }

    this.updateCharts(cords);
    this.updateCanvasPreview(cords);
  }

  //Canvas Preview
  updateCanvasPreview(cords: {x: number, y: number}){
    if(!this.canvas){ return; }

    const { width } = this.canvas.nativeElement;
    const { y, x } = cords;

    const distance = this.pixel_distances[((y - 1) * width) + x - 1]
    if(!distance){ return; }

    const {r, g, b} = this.getPixelColor( this.getNormalizedDistance(distance) );

    this.preview_color = `#${r.toString(16)}${g.toString(16)}${b.toString(16)}`
    this.preview_distnace = this.getformattedDistance(this.getRelativeDistance(distance), this.getScalePrecisionFormat());
  }
  resetCanvasPreview(){
    const {r, g, b} = this.getPixelColor( this.getNormalizedDistance(0) );
    this.preview_color = `#${r.toString(16)}${g.toString(16)}${b.toString(16)}`;
    this.preview_distnace = null;
  }

  //ImageData
  getformattedDistance(distance: number, format: 'mm' | 'cm' | 'm' | null = null): string{
    let decimal = new Decimal(distance);

    const mm = `${decimal.times(1000).toDecimalPlaces(2)}mm`
    const cm = `${decimal.times(100).toDecimalPlaces(2)}cm`
    const m = `${decimal.toDecimalPlaces(2)}m`

    if(format){
      switch (format) {
        case "mm": return mm;
        case "cm": return cm;
        case "m": return m;
      }
    }

    if(distance < 0.01){ return mm; }
    if(distance < 1){ return cm; }

    return m;
  }
  getNormalizedDistance(distance: number): number{
    if(!this.scale_min || !this.scale_max){ return 0; }

    try{
      let normalized = (distance - this.scale_min) / this.scale_range;

      if (normalized > 1) normalized = 1;
      if (normalized < 0) normalized = 0;

      if(Number.isNaN(normalized)){
        normalized = 0;
      }

      return normalized
    }
    catch (e) {
      return 0;
    }
  }
  getRelativeDistance(distance: number, precision: number = 3): number{
    return new Decimal(distance).minus(this.scale_min || '').toDecimalPlaces(precision).toNumber();
  }
  revertRelativeDistance(distance: number, precision: number = 3): number{
    return new Decimal(distance).plus(this.scale_min || '').toDecimalPlaces(precision).toNumber();
  }

  public defineImageData(){
    this.image_data = new ImageData(this.canvas_width, this.canvas_height);
  }
  public setImageData(pixel: number, normalized: number){
    if(!this.image_data){ return; }

    const { r, g, b } = this.getPixelColor(normalized);

    this.image_data.data[pixel] = r;
    this.image_data.data[pixel + 1] = g;
    this.image_data.data[pixel + 2] = b;
    this.image_data.data[pixel + 3] = 255;
  }
  getPixelColor(normalized: number): {r: number, g: number, b: number}{
    const fade = [
      [48, 18, 59],
      [78, 61, 145],
      [36, 150, 209],
      [85, 199, 104],
      [249, 253, 56],
      [242, 121, 30],
      [235, 45, 39],

    ];

    const length = fade.length - 1;
    const idx = normalized * length;
    const i = Math.floor(idx);
    const t = idx - i;

    const [r1, g1, b1] = fade[i];
    const [r2, g2, b2] = fade[Math.min(i + 1, length)];

    const r = Math.round(r1 + (r2 - r1) * t);
    const g = Math.round(g1 + (g2 - g1) * t);
    const b = Math.round(b1 + (b2 - b1) * t)

    return {r, g, b};
  }

  //Scale
  defineScaleBar(){
    const average_distance = this.helpers.averageArr(this.pixel_distances)
    const deviations = this.pixel_distances.map((distance: number) => {
      return Math.abs(distance - average_distance);
    });
    const average_deviation = this.helpers.averageArr(deviations);

    //Define range
    this.scale_min = Infinity;
    this.scale_max = 0;
    for(const distance of this.pixel_distances){
      const deviation = Math.abs(distance - average_distance);
      if(deviation / 5 > average_deviation){ continue; }

      if(distance < this.scale_min){ this.scale_min = distance; }
      if(distance > this.scale_max){ this.scale_max = distance; }
    }

  }
  defineScaleRange(){
    this.scale_range = new Decimal(this.scale_max || 0).minus(this.scale_min || 0).toNumber();
  }
  defineScaleNumbers(){
    if(!this.scale_min || !this.scale_max){ return; }

    this.scale_numbers = [];

    const top = new Decimal(this.scale_max).minus(this.scale_min).toDecimalPlaces(5).toNumber();
    const bottom = 0;
    const format = this.getScalePrecisionFormat();


    for(let i = bottom; i <= top; i += this.getScalePrecision()){
      const decimal = new Decimal(i).toDecimalPlaces(5).toNumber()
      const offset_bottom = new Decimal(decimal).dividedBy(this.scale_range).times(100).toDecimalPlaces(3).toNumber();
      const offset_top = new Decimal(100).minus(offset_bottom).toDecimalPlaces(3).toNumber();

      this.scale_numbers.push({
        number: decimal,
        formatted_number: this.getformattedDistance(decimal, format),
        offset: offset_top,
      });
    }
  }
  getScalePrecision(){
    const base_precisions = [0.0001, 0.00025, 0.0005, 0.001, 0.0025, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10, 25, 50];
    for(const precision of base_precisions){
      if(this.scale_range < precision * 10){
        return precision
      }
    }

    return 100;
  }
  getScalePrecisionFormat(): 'mm' | 'cm' | 'm'{
    const precision = this.getScalePrecision();

    if(precision <= 0.001){ return 'mm' }
    else if(precision <= 0.1){ return 'cm' }

    return 'm';
  }

  async setScale(event: WheelEvent) {
    if (!this.scale_max || !this.scale_min) return;

    const ratio = new Decimal(this.scale_range).times(0.05);
    const delta = event.deltaY > 0 ? ratio : ratio.negated();

    const new_max = new Decimal(this.scale_max).plus(delta);
    if (new_max.lte(this.scale_min)) return;

    this.scale_max = new_max.toNumber();
    this.scale_range = new Decimal(this.scale_max).minus(this.scale_min).toNumber();

    this.defineScaleRange();
    this.defineScaleNumbers();
    await this.drawCanvas();
  }

}
