import {Injectable} from '@angular/core';
import {SceneService} from "../scene/scene.service";
import {ModelsService} from "../models/models.service";
import * as THREE from "three";
import {Vector3} from "three";
import {HelpersService} from "../helpers/helpers.service";
import {UiService} from "../ui/ui.service";

import {InsideNavigationEnums as INE} from "../../enums/InsideNavigationEnums";
import {CalculationsService} from "../calculations/calculations.service";

@Injectable({
    providedIn: 'root'
})
export class CameraService {

    //Lerp
    public camera_lerp: any = {
        target: null,
        last_distance: null,
    }
    public target_lerp: any = {
        target: null,
        last_distance: null,
    }

    //Touch
    public touch_start: any = null;
    public touch_previous: any = null;
    public touch_intersection: any = null;

    //Mouse
    public mouse_down_event: MouseEvent | null = null;

    //Zoom
    public zoom_intersection: any = null;

    constructor(
        private helpers: HelpersService,
        private sceneService: SceneService,
        private modelsService: ModelsService,
        private calculationsService: CalculationsService,
        private uiService: UiService,
    ) {}

    //Camera position
    public setCameraAngle(angle: string) {
        if (!this.modelsService.model_objects.length) {
            return;
        }

        const center = new THREE.Vector3();
        const bounding_box = this.modelsService.getModelBoundingBox();
        bounding_box.getCenter(center);

        const size = new THREE.Vector3();
        bounding_box.getSize(size);

        const max_dimension = Math.max(size.x, size.y, size.z);

        this.sceneService.setTargetPosition(center);

        if (angle === 'TOP') {
            this.setSmoothCameraPosition(new THREE.Vector3(
                center.x,
                center.y + max_dimension * 0.75,
                center.z
            ));
        } else if (angle === 'LEFT') {
            this.setSmoothCameraPosition(new THREE.Vector3(
                center.x - max_dimension * 0.75,
                center.y,
                center.z
            ));
        } else if (angle === 'FRONT') {
            this.setSmoothCameraPosition(new THREE.Vector3(
                center.x,
                center.y,
                center.z + max_dimension * 0.75
            ));
        }
    }
    setSmoothCameraPosition(position: any){
        if(!(position instanceof THREE.Vector3)){
            const { x, y, z } = position;
            position = new Vector3(x, y, z);
        }

        position.x = Number(position.x.toFixed(2));
        position.y = Number(position.y.toFixed(2));
        position.z = Number(position.z.toFixed(2));

        this.camera_lerp.target = position
        this.camera_lerp.last_distance = position.distanceTo(this.sceneService.camera.position);
    }
    setSmoothTargetPosition(position: any){
        if(!(position instanceof THREE.Vector3)){
            const { x, y, z } = position;
            position = new Vector3(x, y, z);
        }

        position.x = Number(position.x.toFixed(2));
        position.y = Number(position.y.toFixed(2));
        position.z = Number(position.z.toFixed(2));

        this.target_lerp.target = position
        this.target_lerp.last_distance = position.distanceTo(this.sceneService.controls.target);
    }

    //Controls
    zoomToObject(event: WheelEvent) {
        switch(this.uiService.perspective){
            case INE.ORBIT: this.zoomToObjectInOrbit(event); break;
            case INE.FIRST_PERSON: this.zoomToObjectInFirstPerson(event); break;
        }
    }
    zoomToObjectInOrbit(event: WheelEvent) {
        const { zoom_speed } = this.uiService;

        if(!this.zoom_intersection){
            this.zoom_intersection = this.calculationsService.getRaycastFromMouse(event, this.modelsService.getModelObjects());
        }

        // @ts-ignore
        const zoom = event.wheelDelta > 0 ? (1 - zoom_speed) : (1 + zoom_speed);

        const camera_position = this.sceneService.getCameraPosition();
        const target_position = this.sceneService.getTargetPosition();

        if (!this.zoom_intersection) {
            this.sceneService.setCameraPosition(this.calculationsService.interpolatePosition(camera_position, target_position, zoom));
            return;
        }

        const zoom_position = this.zoom_intersection.point;
        this.sceneService.setCameraPosition(this.calculationsService.interpolatePosition(camera_position, zoom_position, zoom));
        this.sceneService.setTargetPosition(this.calculationsService.interpolatePosition(target_position, zoom_position, zoom));
    }
    zoomToObjectInFirstPerson(event: WheelEvent){
        const { zoom_speed } = this.uiService;

        if(!this.zoom_intersection){
            const intersection = this.calculationsService.getRaycastFromMouse(event, this.modelsService.getModelObjects());
            if(!intersection){ return; }

            this.zoom_intersection = intersection;
        }


        // @ts-ignore
        const zoom = event.wheelDelta > 0 ? (1 - zoom_speed) : (1 + zoom_speed);

        const camera_position = this.sceneService.getCameraPosition();

        const zoom_position = this.zoom_intersection.point;
        const new_camera_position = this.calculationsService.interpolatePosition(camera_position, zoom_position, zoom)

        const new_target_position = this.sceneService.getTargetPosition();
        new_target_position.x -= (camera_position.x - new_camera_position.x);
        new_target_position.z -= (camera_position.z - new_camera_position.z);
        new_target_position.y -= (camera_position.y - new_camera_position.y);

        this.sceneService.setCameraPosition(new_camera_position);
        this.sceneService.setTargetPosition(new_target_position);
    }

    zoomToObjectMobile(event: any, touch_start: any, touch_previous: any) {
        const { controls } = this.sceneService;

        if(controls.touches.ONE !== 'ZOOM'){ return; }

        const difference =  (touch_previous ? touch_previous.clientY : touch_start.clientY) - event.clientY;
        const zoom = 1 - difference / 250;

        const camera_position = this.sceneService.getCameraPosition();
        const target_position = this.sceneService.getTargetPosition();

        if (!this.touch_intersection) {
            this.sceneService.setCameraPosition(this.calculationsService.interpolatePosition(camera_position, target_position, zoom));
            return;
        }

        const zoom_position = this.touch_intersection.point;
        this.sceneService.setCameraPosition(this.calculationsService.interpolatePosition(camera_position, zoom_position, zoom));
        this.sceneService.setTargetPosition(this.calculationsService.interpolatePosition(target_position, zoom_position, zoom));
    }

    //Anchror point set
    async updateCameraTarget(event: MouseEvent) {
        if(event.button !== 1 || !this.uiService.rendering || this.uiService.is2D() || this.uiService.perspective == INE.FIRST_PERSON){ return; }

        const intersection = this.calculationsService.getRaycastFromMouse(event, this.modelsService.getModelObjects());
        if(!intersection){ return; }

        this.uiService.setCss('cursor', 'none');

        if(!this.modelsService.sprites.camera_anchor){
            this.modelsService.sprites.camera_anchor = 'PLACING'
            this.modelsService.sprites.camera_anchor = await this.helpers.createSprite('svg/camera_anchor.svg')
        }

        this.modelsService.sprites.camera_anchor.position.copy(intersection.point);

        const distance = this.sceneService.getCameraPosition().distanceTo(this.modelsService.sprites.camera_anchor.position);
        const size = distance / 20

        this.modelsService.sprites.camera_anchor.scale.set(size, size, size);

        this.sceneService.add(this.modelsService.sprites.camera_anchor);
        this.setSmoothTargetPosition(intersection.point);
    }
    closeCameraTarget(){
        this.uiService.setCss('cursor', 'default');
        const { camera_anchor } = this.modelsService.sprites;

        if(!camera_anchor){ return; }
        else if(camera_anchor == 'PLACING'){
            setTimeout(this.closeCameraTarget.bind(this), 10);
            return;
        }

        this.sceneService.remove(camera_anchor);
        this.modelsService.sprites.camera_anchor = null;
    }

    //Mobile zoom point
    async placeMobileZoomPoint(touch: any){
        if(this.sceneService.controls.touches.ONE !== 'ZOOM' || !this.uiService.rendering){ return; }

        const intersection = this.calculationsService.getRaycastFromMouse(touch, this.modelsService.getModelObjects());
        if(!intersection){ return; }

        if(!this.modelsService.sprites.zoom_anchor){
            this.modelsService.sprites.zoom_anchor = 'PLACING';
            this.modelsService.sprites.zoom_anchor = await this.helpers.createSprite('svg/zoom_anchor.svg')
        }

        this.modelsService.sprites.zoom_anchor.position.copy(intersection.point);

        const distance = this.sceneService.getCameraPosition().distanceTo(this.modelsService.sprites.zoom_anchor.position);
        const size = distance / 20

        this.modelsService.sprites.zoom_anchor.scale.set(size, size, size);

        this.sceneService.add(this.modelsService.sprites.zoom_anchor);
    }
    removeMobileZoomPoint(){
        const { zoom_anchor } = this.modelsService.sprites;

        if(!zoom_anchor){ return; }
        else if(zoom_anchor == 'PLACING'){
            setTimeout(this.removeMobileZoomPoint.bind(this), 10);
            return;
        }

        this.sceneService.remove(this.modelsService.sprites.zoom_anchor);
        this.modelsService.sprites.zoom_anchor = null;
    }

    //Lerp
    lerp(){
        if(this.camera_lerp.target){
            this.sceneService.camera.position.lerp(this.camera_lerp.target, 0.075);

            const distance = this.sceneService.camera.position.distanceTo(this.camera_lerp.target);
            const diff = Math.abs(this.camera_lerp.last_distance - distance);

            this.camera_lerp.last_distance = distance;

            if(diff < 0.005){
                this.camera_lerp.target = null;
            }

        }

        if(this.target_lerp.target){
            this.sceneService.controls.target.lerp(this.target_lerp.target, 0.075);

            const distance = this.sceneService.controls.target.distanceTo(this.target_lerp.target);
            const diff = Math.abs(this.target_lerp.last_distance - distance);

            this.target_lerp.last_distance = distance;

            if(diff < 0.005){
                this.target_lerp.target = null;
            }

        }

    }
    unsetLerp(){
        this.camera_lerp.target = null;
        this.target_lerp.target = null;
    }

    //First Person
    firstPersonRotate(event: MouseEvent | TouchEvent) {
        if(this.uiService.perspective !== INE.FIRST_PERSON){ return; }
        if(!this.touch_start && (!this.mouse_down_event || this.mouse_down_event.button !== 0)) { return; }
        if(!this.sceneService.controls.enabled){ return; }

        const camera = this.sceneService.camera;
        const controls = this.sceneService.controls;
        const target = controls.target;

        const sensitivity = 0.0015;
        const mobile_sensitivity = 0.004;
        let deltaX, deltaY;

        if(event instanceof MouseEvent){
            deltaX = event.movementX * sensitivity;
            deltaY = -event.movementY * sensitivity;
        }
        else{
            if(!this.touch_previous){ return; }

            const touch = event.touches[0];
            deltaX = (touch.clientX - this.touch_previous.clientX) * mobile_sensitivity;
            deltaY = -(touch.clientY - this.touch_previous.clientY) * mobile_sensitivity;
        }

        const direction = new THREE.Vector3().subVectors(target, camera.position);
        const rotationMatrixY = new THREE.Matrix4().makeRotationY(deltaX);
        direction.applyMatrix4(rotationMatrixY);

        const cameraUp = new THREE.Vector3(0, 1, 0); // World up
        const cameraRight = new THREE.Vector3().crossVectors(direction, cameraUp).normalize();
        const rotationMatrixX = new THREE.Matrix4().makeRotationAxis(cameraRight, -deltaY);
        direction.applyMatrix4(rotationMatrixX);

        target.copy(camera.position).add(direction);
    }

    //PC Listeners
    onWindowScroll(event: WheelEvent){
        this.unsetLerp();
        this.zoomToObject(event);
    }
    onMouseDown(event: MouseEvent){
        this.mouse_down_event = event;
        const { button } = event;

        if(button === 0 || (button === 2 && this.uiService.perspective === INE.ORBIT)){
            this.sceneService.controlling = true;
            this.uiService.setCss('cursor', 'grabbing');
        }

        this.unsetLerp();
        this.updateCameraTarget(event)
    }
    onMouseUp(event: MouseEvent){
        this.mouse_down_event = null;
        const { button } = event;

        if(button === 0 || (button === 2 && this.uiService.perspective === INE.ORBIT)){
            this.sceneService.controlling = false;
            this.uiService.setCss('cursor', 'default');
        }

        this.closeCameraTarget();
    }
    onMouseMove(event: MouseEvent){
        this.zoom_intersection = null;

        //Unset in case of mousemove during target change, except scroll button
        if([0, 2].includes(this.mouse_down_event?.button || -1)){
            this.unsetLerp();
        }

        this.firstPersonRotate(event);
    }

    //Mobile Listeners
    onTouchStart(event: TouchEvent){
        const touch = event.touches[0];

        this.touch_start = touch;
        this.touch_intersection = this.calculationsService.getRaycastFromMouse(touch, this.modelsService.getModelObjects());

        this.placeMobileZoomPoint(touch)
    }
    onTouchEnd(event: TouchEvent){
        this.touch_start = null;
        this.touch_previous = null;
        this.touch_intersection = null;
        this.removeMobileZoomPoint();
    }
    onTouchMove(event: TouchEvent){
        const touch = event.touches[0];
        this.zoomToObjectMobile(touch, this.touch_start, this.touch_previous);
        this.firstPersonRotate(event);
        this.unsetLerp();

        this.touch_previous = touch;
    }

    //Lifecycle
    update(){
        this.lerp()
    }

}
