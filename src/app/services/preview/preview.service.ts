import {ElementRef, Injectable} from '@angular/core';
import {ModelPreview} from "../../models/model_preview/model-preview";
import {HelpersService} from "../helpers/helpers.service";
import {ModelsService} from "../models/models.service";
import {SceneService} from "../scene/scene.service";
import {Location} from "../../models/location/location.model";
import {Mesh} from "three";
import {UiService} from "../ui/ui.service";

@Injectable({
  providedIn: 'root'
})
export class PreviewService {

  public state: boolean = false;
  public available: boolean = false;

  // Images
  public images: ModelPreview[] = [];
  public closest_image: ModelPreview | null = null;

  // Markers
  public markers_state = false;
  public markers: Mesh[] = [];

  //Zoom
  public zoom = {
    scale: 1,
    is_dragging: false,
    x: 0,
    y: 0,

    start_x: 0,
    start_y: 0,
  }
  public image_style = {
    transform: 'scale(1)'
  }

  constructor(
      protected helpers: HelpersService,
      protected modelsService: ModelsService,
      protected sceneService: SceneService,
      protected uiService: UiService,
  ) { }

  // Init
  async load(){
    await this.setImages()
    this.setCameraListener()
    this.setClosestImage();
    this.setAvailable();
  }
  async setImages(){
    if(!this.modelsService.model){ return }

    try{
      const { data } = await this.helpers.post('api/models/previews', { guid: this.modelsService.model.guid });
      this.images = data.images
          .map((image: any) => new ModelPreview(image))
          .filter((image: ModelPreview) => image.location);
    }
    catch (e) {}
  }
  setCameraListener(){
    this.sceneService.controls.addEventListener('change', this.setClosestImage.bind(this));
  }
  setClosestImage(){
    if(!this.modelsService.location){ return }

    const { lat, lon } = this.modelsService.location.calculateLocation( this.sceneService.getCameraPosition(), this.modelsService.getSpritePosition('google_maps') );
    const location = new Location(`${lat}, ${lon}`);
    const previous_image_id = this.closest_image?.id;

    for(const image of this.images){
      if(!image.location){ continue; }

      const distance = location.distanceToLocation( image.location );
      if(!this.closest_image?.location || distance < location.distanceToLocation( this.closest_image.location )){
        this.closest_image = image;

        // Reset marker highlight only if the closese image changes
        if(this.markers_state && this.closest_image.id !== previous_image_id){
          this.clearMarkers();
          this.appendMarkers();
        }
      }
    }
  }
  setAvailable(){
    if(!this.images.length || !this.closest_image){ return; }
    if(this.helpers.isMobile()){ return; }
    if(this.uiService.is2D()){ return; }

    this.available = true;
  }

  //Preview
  togglePreview(){


    this.uiService.toggle('preview_position', this.state ? 200 : 0);
    this.uiService.toggle('preview_size', this.state ? 0 : 200);
    this.uiService.toggle('preview_upscale', this.state ? 400 : 0);
    this.uiService.toggle('preview_img', 0);
    this.uiService.toggle('preview_img', 800);

    setTimeout(() => {
      this.state = !this.state
      this.resetZoomStyle();
    }, 150);
  }

  //Markers
  toggleMarkers(){
    this.markers_state
      ? this.clearMarkers()
      : this.appendMarkers();

    this.markers_state = !this.markers_state;
  }
  clearMarkers(){
    this.markers.forEach((marker: any) => {
      this.sceneService.remove(marker);
    });

    this.markers = [];
  }
  appendMarkers(){
    this.images.forEach((image: ModelPreview) => {
      if(!image.location){ return; }

      const position = this.modelsService.location.calculatePosition( image.location, this.modelsService.getSpritePosition('google_maps') );

      const marker = this.helpers.createCylinder((image.id == this.closest_image?.id ? '#e5b80b' : '#FFFFFF'), 0.2, 100, .66);
      marker.position.copy( position );

      this.sceneService.add( marker );
      this.markers.push( marker );
    });
  }

  //Zoom
  zoomIn(){
    this.zoom.scale = this.zoom.scale * 1.1;
    this.updateZoomStyle();
  }
  zoomOut(){
    this.zoom.scale = Math.max(1, this.zoom.scale * 0.9);
    this.updateZoomStyle();
  }

  //Listeners
  onZoom(event: WheelEvent) {
    if (!this.closest_image) return;

    //transform image size, and update x,y to keep the same cursor position
    const img = event.target as HTMLElement;
    const rect = img.getBoundingClientRect();

    const prev_scale = this.zoom.scale;
    const factor = event.deltaY > 0 ? 0.9 : 1.1;

    let new_scale = prev_scale * factor;
    new_scale = Math.max(1, new_scale);

    const change = new_scale - prev_scale;
    if (change === 0) return;

    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const offsetX = x - rect.width / 2;
    const offsetY = y - rect.height / 2;

    this.zoom.x -= (offsetX / prev_scale) * change;
    this.zoom.y -= (offsetY / prev_scale) * change;

    this.zoom.scale = new_scale;

    this.updateZoomStyle();
  }
  onMouseDown(event: MouseEvent){
    if(event.button !== 0){ return; }

    this.zoom.is_dragging = true;
    this.zoom.start_x = event.clientX;
    this.zoom.start_y = event.clientY;

    this.uiService.setCss('cursor', 'grabbing');
  }
  onMouseUp(event: MouseEvent){
    if(event.button !== 0){ return; }
    this.zoom.is_dragging = false;
    this.uiService.setCss('cursor', 'default');
  }
  onMouseMove(event: MouseEvent){
    if(!this.zoom.is_dragging || this.zoom.scale <= 1){ return; }

    this.zoom.x += event.clientX - this.zoom.start_x;
    this.zoom.y += event.clientY - this.zoom.start_y;
    this.zoom.start_x = event.clientX;
    this.zoom.start_y = event.clientY;

    this.updateZoomStyle();
  }

  updateZoomStyle() {
    if(!this.closest_image){ return }

    //Adjust for padding and buttons
    const width = this.closest_image.fullscreen().width - 18;
    const height = this.closest_image.fullscreen().height - 49;

    const scaled_width = width * this.zoom.scale;
    const scaled_height = height * this.zoom.scale;

    const max_x = (scaled_width - width) / 2;
    const max_y = (scaled_height - height) / 2;

    this.zoom.x = Math.max(-max_x, Math.min(this.zoom.x, max_x));
    this.zoom.y = Math.max(-max_y, Math.min(this.zoom.y, max_y));

    this.image_style.transform = `translate(${this.zoom.x}px, ${this.zoom.y}px) scale(${this.zoom.scale})`;
  }
  resetZoomStyle(){
    this.zoom.scale = 1;
    this.zoom.x = 0;
    this.zoom.y = 0;
    this.updateZoomStyle();
  }

}
