import {Injectable, signal} from '@angular/core';
import {HelpersService} from "../helpers/helpers.service";
import {SceneService} from "../scene/scene.service";
import {ModelsService} from "../models/models.service";
import * as THREE from "three";
import {Mesh} from "three";
import {CalculationsService} from "../calculations/calculations.service";

@Injectable({
  providedIn: 'root'
})
export class BaseIndicatorService {

  public static COLOR_INDICATOR = '#5555FF';
  public static COLOR_CONFIRM = '#55ff55';
  public static COLOR_REMOVE = '#FF5555';
  public static COLOR_HIGHLIGHT = '#FFD700';
  public static COLOR_SELECT = '#fb8300';

  public state: boolean = false;

  //Indicator
  public indicator: any = null;
  public indicator_color = '#5555FF';
  public indicator_confirmed_color = "#55ff55";
  public indicator_remove_color = "#FF5555";
  public indicator_highlight_color = "#FFD700";
  public indicator_radius: number = 0.1

  //Raycast
  public raycast_objects: THREE.Object3D[] = []
  public raycast_intersection: any = null;
  public raycast_callback: (() => any) | null = null;

  //Points
  public markers: Mesh[] = [];
  public markers_signal = signal('');

  public indicator_options = {
    copy_face_quaternion: false,
    place_on_markers: false,
  }

  constructor(
    protected helpers: HelpersService,
    protected sceneService: SceneService,
    protected calculationsService: CalculationsService,
    protected modelsService: ModelsService,
  ) { }



  //Indicator
  toggleIndicator(options: { state?: boolean, clear?: boolean } = {}){
    //Set State
    this.state = !this.state;
    if(options.state !== undefined){
      this.state = options.state;
    }

    if(this.state){
      this.appendIndicator()
      return
    }

    this.removeIndicator();
    if(options.clear !== false){
      this.clearIndicatorPoints();
    }
  };
  moveIndicator(event: MouseEvent){
    if(!this.state){ return; }

    this.indicator.visible = true;
    if(this.sceneService.controlling){
      this.indicator.visible = false;
      return;
    }


    if(this.indicator_options.place_on_markers){
      const marker_intersection = this.calculationsService.getRaycastFromMouse(event, this.markers);
      if(marker_intersection){
        const { object } = marker_intersection;

        object.visible = false

        this.indicator.position.copy(object.position);
        return
      }
      this.markers.map((dot: any) => dot.visible = true);
    }

    this.raycast_intersection = this.calculationsService.getRaycastFromMouse(event, this.getRaycastModels());
    if (!this.raycast_intersection) {
      return;
    }

    this.indicator.position.copy(this.raycast_intersection.point);

    this.copyIndicatorQuaternion();
    if(this.raycast_callback){
      this.raycast_callback();
    }
  }
  copyIndicatorQuaternion(){
    if(!this.indicator_options.copy_face_quaternion || !this.indicator){ return; }

    const intersection = this.raycast_intersection;
    const object = intersection.object;
    const geometry = object.geometry as THREE.BufferGeometry;

    const index = geometry.index!;
    const normals = geometry.attributes['normal'];
    const hitFaceIndex = intersection.faceIndex!;

    const avgNormal = new THREE.Vector3();
    const faceRange = 4;

    for (let offset = -faceRange; offset <= faceRange; offset++) {
      const faceIdx = hitFaceIndex + offset;
      if (faceIdx < 0 || faceIdx >= index.count / 3) continue;

      const i0 = index.getX(faceIdx * 3 + 0);
      const i1 = index.getX(faceIdx * 3 + 1);
      const i2 = index.getX(faceIdx * 3 + 2);

      const n0 = new THREE.Vector3().fromBufferAttribute(normals, i0);
      const n1 = new THREE.Vector3().fromBufferAttribute(normals, i1);
      const n2 = new THREE.Vector3().fromBufferAttribute(normals, i2);

      avgNormal.add(n0).add(n1).add(n2);
    }

    avgNormal.normalize();
    avgNormal.transformDirection(object.matrixWorld);

    const quat = new THREE.Quaternion();
    quat.setFromUnitVectors(new THREE.Vector3(0, 0, 1), avgNormal);
    this.indicator.quaternion.copy(quat);
  }
  moveIndicatorNonCanvas(){
    if(!this.state){ return; }
    this.indicator.visible = false;
  }
  appendIndicator(){
    this.indicator = this.generateIndicator();

    this.indicator.visible = false;
    this.sceneService.add(this.indicator);
  }
  generateIndicator(): THREE.Mesh{
    return this.helpers.createDot(this.indicator_color, this.indicator_radius);
  }
  removeIndicator(){
    this.sceneService.remove(this.indicator);
  }
  setIndicatorRadius(){
    this.removeIndicator();
    this.appendIndicator();

    for(const i in this.markers){
      const marker = this.markers[i];
      const position = marker.position.clone();
      this.sceneService.remove(marker);

      const new_marker = this.createMarker(position);
      this.markers[i] = new_marker;
      this.sceneService.add(new_marker);
    }
  }

  //Indicator Points
  createMarker(position?: THREE.Vector3){
    const marker = this.indicator.clone();

    marker.visible = true;
    marker.material = marker.material.clone();
    marker.material.color.set(this.indicator_confirmed_color);

    if(position){
      marker.position.copy(position)
    }

    return marker;
  }
  placeMarker(event: MouseEvent): THREE.Mesh | void{
    if(!this.state || event.button !== 0) { return; }

    const marker = this.createMarker();

    this.sceneService.add(marker);
    this.markers.push(marker);
    this.setMarkersSignal();

    return marker;
  }
  clearIndicatorPoints(){
    for(const marker of this.markers){
      this.sceneService.remove(marker);
    }

    this.markers = [];
    this.setMarkersSignal();
  }

  //Raycast
  getRaycastModels(){
    if(!this.raycast_objects.length){
      this.raycast_objects = this.modelsService.getModelObjects();
    }

    return this.raycast_objects;
  }
  setRaycastModels(objects: THREE.Object3D[]){
    this.raycast_objects = objects;
  }
  setRaycastCallback(fn: () => any){
    this.raycast_callback = fn;
  }

  //Highlight
  highlightMarker(event: MouseEvent){
    if(!this.state || event.button !== 2){ return; }

    const markers_intersection = this.calculationsService.getRaycastFromMouse(event, this.markers);
    if(!markers_intersection){ return; }

    this.indicator.material.color.set(this.indicator_remove_color);
  }
  highlightMarkerByIndex(index: number){
    const marker = this.markers[index];
    (marker.material as THREE.MeshBasicMaterial).color.set(this.indicator_highlight_color);
  }
  highlightRemoveMarkerByIndex(index: number){
    const marker = this.markers[index];
    (marker.material as THREE.MeshBasicMaterial).color.set(this.indicator_remove_color);
  }
  dehighlightMarkerByIndex(index: number){
    const marker = this.markers[index];
    (marker.material as THREE.MeshBasicMaterial).color.set(this.indicator_confirmed_color);
  }

  removeMarker(event: MouseEvent){
    if(!this.state || event.button !== 2){ return; }

    this.indicator.material.color.set(this.indicator_color);

    const marker_intersection = this.calculationsService.getRaycastFromMouse(event, this.markers);
    if(!marker_intersection){ return; }

    const index = this.markers.findIndex((dot: THREE.Mesh) => {
      return dot.uuid == marker_intersection.object.uuid;
    })
    this.removeMarkerByIndex(index)
  }
  removeMarkerByIndex(index: number){
    const dot = this.markers[index];
    this.markers.splice(index, 1);
    this.sceneService.remove(dot);
    this.setMarkersSignal();
  }

  //Signal
  setMarkersSignal(){
    this.markers_signal.set( this.helpers.randomString(8) );
  }

}
