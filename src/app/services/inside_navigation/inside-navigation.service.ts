import {Injectable} from '@angular/core';
import * as THREE from "three";
import {ModelsService} from "../models/models.service";
import {HelpersService} from "../helpers/helpers.service";
import {SceneService} from "../scene/scene.service";
import {CameraService} from "../camera/camera.service";
import {InsideNavigationEnums as INE} from "../../enums/InsideNavigationEnums";
import {UiService} from "../ui/ui.service";
import {CalculationsService} from "../calculations/calculations.service";


@Injectable({
  providedIn: 'root'
})
export class InsideNavigationService {

  constructor(
      private helpers: HelpersService,
      private modelsService: ModelsService,
      private calculationsService: CalculationsService,
      private sceneService: SceneService,
      private cameraService: CameraService,
      private uiService: UiService
  ) { }

  //View
  selectView(view: string){
    this.uiService.setPerspective(view as INE);

    if(this.uiService.perspective == INE.ORBIT){
      this.sceneService.setTouchesOne(THREE.TOUCH.ROTATE);
      this.sceneService.setLeftButton(THREE.MOUSE.ROTATE);
      this.sceneService.setMiddleButton(THREE.MOUSE.ROTATE);
      this.sceneService.setRightButton(THREE.MOUSE.PAN);
      this.sceneService.setPolarAngle(0, 0.5);

    }
    else if(this.uiService.perspective == INE.FIRST_PERSON){
      this.sceneService.setTouchesOne(null);
      this.sceneService.setLeftButton(null);
      this.sceneService.setMiddleButton(null);
      this.sceneService.setRightButton(null);
      this.sceneService.setPolarAngle(.15, 0.85);
    }
  }

  //Navigation
  async navigate(event: MouseEvent){
    if(this.sceneService.controlling){
      this.modelsService.clearSprite('inside_navigation_anchor');
      return;
    }

    if(this.uiService.perspective != INE.FIRST_PERSON){ return; }

    const floor_planes = this.modelsService.getSelectionsObjects('floor');
    const intersection = this.calculationsService.getRaycastFromMouse(event, floor_planes);

    if(!intersection){
      this.modelsService.clearSprite('inside_navigation_anchor')
      return;
    }

    if(!this.modelsService.sprites.inside_navigation_anchor){
      this.modelsService.sprites.inside_navigation_anchor = 'PLACING'
      this.modelsService.sprites.inside_navigation_anchor = await this.helpers.createTexturedPlane('svg/inside_navigation_anchor.svg', .5, .5)
    }

    if(this.modelsService.sprites.inside_navigation_anchor == 'PLACING'){ return; }

    this.modelsService.sprites.inside_navigation_anchor.visible = true;
    this.modelsService.sprites.inside_navigation_anchor.position.copy(intersection.point);
    this.sceneService.add(this.modelsService.sprites.inside_navigation_anchor);

  }
  async move(event: MouseEvent){
    if(this.uiService.perspective != INE.FIRST_PERSON){ return; }

    const objects = [ ...this.modelsService.getSelectionsObjects('floor'), ...this.modelsService.getModelObjects() ];
    const intersection = this.calculationsService.getRaycastFromMouse(event, objects);

    if(!intersection || !this.modelsService.isObjectSelectionType(intersection.object, 'floor')){ return; }

    //Camera
    const point = intersection.point.clone();
    point.y += 1.65;

    const y_diff = this.sceneService.camera.position.y - point.y;
    const x_diff = this.sceneService.camera.position.x - point.x;
    const z_diff = this.sceneService.camera.position.z - point.z;

    console.log(this.sceneService.camera.position.distanceTo( this.sceneService.controls.target ));

    this.cameraService.setSmoothCameraPosition(point);

    //Target
    const target_position = this.sceneService.controls.target.clone();
    target_position.y -= y_diff;
    target_position.x -= x_diff;
    target_position.z -= z_diff;

    this.cameraService.setSmoothTargetPosition(target_position);
  }

}
