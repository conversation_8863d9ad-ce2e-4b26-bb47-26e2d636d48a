import { Injectable } from '@angular/core';
import {HelpersService} from "../helpers/helpers.service";
import {SceneService} from "../scene/scene.service";
import {ModelsService} from "../models/models.service";
import {Location} from "../../models/location/location.model";
import {Vector3} from "three";

@Injectable({
  providedIn: 'root'
})
export class PreloadService {

  constructor(
      private helpers: HelpersService,
      private sceneService: SceneService,
      private modelService: ModelsService,
  ) { }

  async load(){
    this.preloadCameraPosition();
    this.preloadTargetPosition();
  }

  private preloadCameraPosition(){
    const [lat, lon, relative_y] = this.helpers._get(['camera_lat', 'camera_lon', 'camera_maps_relative_y'])
    const vector = new Vector3();
    const maps_vector = this.modelService.getSpritePosition('google_maps');

    if(!lat || !lon || !relative_y || !maps_vector){ return; }

    const target_location = new Location( `${lat}, ${lon}` );
    const position = this.modelService.location.calculatePosition( target_location, maps_vector );

    vector.setX( position.x );
    vector.setZ( position.z );
    vector.setY( maps_vector.y + Number(relative_y) );

    this.sceneService.setCameraPosition( vector );
  }
  private preloadTargetPosition(){
    const [lat, lon, relative_y] = this.helpers._get(['target_lat', 'target_lon', 'target_maps_relative_y'])
    const vector = new Vector3();
    const maps_vector = this.modelService.getSpritePosition('google_maps');

    if(!lat || !lon || !relative_y || !maps_vector){ return; }

    const target_location = new Location( `${lat}, ${lon}` );
    const position = this.modelService.location.calculatePosition( target_location, maps_vector );

    vector.setX( position.x );
    vector.setZ( position.z );
    vector.setY( maps_vector.y + Number(relative_y) );
    this.sceneService.setTargetPosition( vector );
  }

}
