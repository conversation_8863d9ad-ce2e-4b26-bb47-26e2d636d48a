import { Injectable } from '@angular/core';
import {BaseIndicatorService} from "../BaseIndicator/base-indicator.service";
import {HelpersService} from "../helpers/helpers.service";
import {SceneService} from "../scene/scene.service";
import {MeasurementsService} from "../measurements/measurements.service";
import {ModelsService} from "../models/models.service";
import * as THREE from "three";
import {ModelComment} from "../../models/model_comment/modelComment";
import {CameraService} from "../camera/camera.service";
import {UiService} from "../ui/ui.service";
import {ParsedDate} from "../../models/ParsedDate/parsed-date";
import {AuthService} from "../auth/auth.service";
import {CalculationsService} from "../calculations/calculations.service";

@Injectable({
  providedIn: 'root'
})
export class CommentsService extends BaseIndicatorService{

  public step: null | 'select' | 'confirm' = null
  public can_comment: boolean = false;

  public loading: boolean = false;
  public comment: string = '';
  public marker: THREE.Mesh | null = null;
  public pointer: THREE.Mesh | null = null;
  public pointer_timeout: number | null = null

  public comments: ModelComment[] = [];

  constructor(
    helpers: HelpersService,
    sceneService: SceneService,
    calculationsService: CalculationsService,
    modelsService: ModelsService,
    protected cameraService: CameraService,
    protected uiService: UiService,
    protected authService: AuthService,
  ) {
    super(helpers, sceneService, calculationsService, modelsService);
  }

  init(){
    this.defineComments();
    this.definePerimssion()
  }
  toggle(){
    if(this.step){
      this.clear();
      return;
    }

    this.step = 'select';
    this.toggleIndicator();
  }
  clear(){
    this.step = null;
    this.comment = '';

    this.clearMarker();
    this.clearPointerTimeout();

    this.toggleIndicator({ state: false });
  }
  confirm(event: MouseEvent){
    if(this.step !== 'select'){ return; }

    this.placeMarker(event);
    if(!this.markers.length){ return; }

    this.setMarker();
    this.toggleIndicator({ state: false });
    this.step = 'confirm';
  }

  //Marker
  setMarker(){
    const position = this.markers[0].position.clone();
    this.marker = this.helpers.createDot(this.indicator_confirmed_color, 0.1);
    (this.marker.material as THREE.Material).opacity = .4;
    this.marker.position.copy(position);

    this.sceneService.add(this.marker);
  }
  clearMarker(){
    this.sceneService.remove(this.marker);
    this.marker = null;
  }

  //Comment
  defineComments(){
    this.comments = this.modelsService.model?.comments || [];
  }
  async storeComment(){
    if(!this.marker){ return; }

    this.loading = true;

    const position = this.marker.position.clone();
    const camera = this.sceneService.getCameraPosition();

    const { data } = await this.helpers.post('/api/editor/comment/store', {
      model_id: this.modelsService.model?.id,
      comment: this.comment,
      camera: JSON.stringify(camera),
      position: JSON.stringify(position),
    });
    this.comments.unshift( new ModelComment(data.comment) )

    this.loading = false;
    this.toggle();
  }
  async deleteComment(comment: ModelComment){
    const remove = async () => {
      await this.helpers.post('/api/editor/comment/delete', { guid: comment.guid });
      this.comments = this.comments.filter((model_comment: ModelComment) => model_comment.guid != comment.guid
      );
    }

    this.uiService.showConfirmModal({
      title: 'Opmerkinge verwijderen',
      message: `Weet je zeker dat je de opmerking wilt verwijderen?`,
      confirm_text: 'Verwijderen',
      onConfirm: remove
    });
  }
  showComment(comment: ModelComment){
    this.cameraService.setSmoothCameraPosition(comment.camera);
    this.cameraService.setSmoothTargetPosition(comment.position)
    this.appendPointer(comment)
  }
  commentIsValid(){
    return this.comment.length > 0 && this.comment.length <= 255;
  }

  //Pointer
  appendPointer(comment: ModelComment){
    this.clearPointer();
    this.clearPointerTimeout();

    this.pointer = this.helpers.createDot(this.indicator_highlight_color, 0.1);
    (this.pointer.material as THREE.Material).opacity = .4;
    this.pointer.position.copy(comment.position);

    this.sceneService.add(this.pointer);

    this.clearPointer(5000);
  }
  clearPointer(timeout: number | null = null){
    const clear = () => {
      this.sceneService.remove(this.pointer);
      this.pointer = null;
    }

    if(timeout){
      this.pointer_timeout = window.setTimeout(clear.bind(this), timeout);
      return;
    }

    clear()
  }
  clearPointerTimeout(){
    if(!this.pointer_timeout){ return; }

    clearTimeout(this.pointer_timeout);
    this.pointer_timeout = null;
  }

  //Dates
  getDates(): ParsedDate[] {
    let dates: ParsedDate[] = [];
    this.comments.forEach((comment: ModelComment) => {
      const exists = dates.find((date: ParsedDate) => date.formattedISO == comment.date.formattedISO);
      if(exists){ return; }

      dates.push(comment.date);
    });

    return dates;
  }
  commentsByDate(ISO: string){
    return this.comments.filter((comment: ModelComment) => {
      return comment.date.formattedISO == ISO;
    });
  }

  //Permissions
  definePerimssion(){
    this.can_comment = this.authService.hasPermission('opmerkingen_toevoegen') || (this.authService.model_customer_same_as_user && this.authService.hasPermission('eigen_modellen_opmerkingen_toevoegen'))
  }

}
