import { Injectable } from '@angular/core';
import * as THREE from "three";
import {OrbitControls} from "three/examples/jsm/controls/OrbitControls.js";
import {TransformControls} from "three/examples/jsm/controls/TransformControls.js";
import {UiService} from "../ui/ui.service";
import {HelpersService} from "../helpers/helpers.service";
import {Vector3} from "three";
import {Decimal} from "decimal.js";

@Injectable({
    providedIn: 'root'
})
export class SceneService {

  private canvasRef: any;

  //THREE
  public scene = new THREE.Scene();
  public renderer: any;
  public camera: any;
  public controls: any;
  public transformControls: any;

  //Objects
  public lightning: any = {
    ambient: null,
    state: false,
    intensity: 2,
  }

  //Controls
  public controlling = false;

  constructor(
      private uiService: UiService,
      protected helpers: HelpersService,
  ) {}


  //Init
  init( cavnasRef: any ) {
    this.canvasRef = cavnasRef;

    this.initScene()
    this.initLight()
    this.initControls();
    this.initTransformer();
    this.initGrid();
  }
  initScene() {
    const canvas = this.canvasRef.nativeElement

    this.renderer = new THREE.WebGLRenderer({canvas, preserveDrawingBuffer: true});
    this.renderer.setSize(window.innerWidth, window.innerHeight);

    this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    this.camera.position.set(10, 10, 10);
  }
  initLight() {
    this.lightning.ambient = new THREE.AmbientLight(0xffffff, this.lightning.intensity);
    this.add(this.lightning.ambient);
  }
  initControls(){
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);

    //Controls buttons
    this.controls.enableZoom = false;
    this.controls.touches.TWO = null;

    //target
    this.controls.target.set(0, 0, 0);

    //2D Controls
    if(this.uiService.is2D()){
      this.setLeftButton(THREE.MOUSE.ROTATE);
      this.setMiddleButton(null);
      this.setRightButton(THREE.MOUSE.PAN);

      this.setTouchesOne(THREE.TOUCH.PAN);

      this.setPolarAngle(0, 0)
    }


    this.controls.addEventListener('start', this.onControlStart.bind(this));
    this.controls.addEventListener('end', this.onControlEnd.bind(this));
    this.controls.update();
  }
  initTransformer() {
    this.transformControls = new TransformControls(this.camera, this.renderer.domElement);
    this.transformControls.addEventListener('dragging-changed', (event: any) => {
      this.controls.enabled = !event.value;
    });
    this.transformControls.addEventListener("objectChange", this.transformChildren.bind(this))

    this.scene.add(this.transformControls);
  }
  initGrid(){
    if(!this.helpers._get('grid')){ return; }

    const grid = new THREE.GridHelper(20, 10);
    this.scene.add(grid);
  }

  //Transformer
  transform(object: THREE.Object3D | null | undefined, children: THREE.Object3D[] = []) {
    if(!object){ return; }

    this.transformControls.position_children = children;
    this.transformControls.last_position = object.position.clone();

    if(object.uuid != this.transformControls.object?.uuid){
      const center = this.helpers.getObjectCenter(object);
      const size = this.helpers.getObjectMaxDiagonalSize(object) / 50;
      const clamped_size = Math.min(Math.max(size, 0.2), 2)

      this.transformControls.position.copy(center);
      this.transformControls.setSize(clamped_size);
      this.transformControls.attach(object);
      return
    }

    this.transformControls.detach(object);
  }
  transformDetach(object: THREE.Object3D | null | undefined){
    if(!object){ return; }

    if(!this.isTransformerAttached( object.uuid )){ return; }
    this.transform(object);
  }
  transformChildren(){
    const object = this.transformControls.object;
    if(!object){ return; }

    for(const child of this.transformControls.position_children){
      const delta = new THREE.Vector3().subVectors(object.position, this.transformControls.last_position);
      child.position.add(delta);
    }

    this.transformControls.last_position.copy(object.position.clone());
  }
  isTransformerAttached(uuid: string | null = null): boolean{
    if(uuid){
      return this.transformControls?.object?.uuid == uuid;
    }

    return !!this.transformControls.object;
  }

  //Controls
  getTargetPosition(options: any = {}): Vector3{
    const position: Vector3 = this.controls.target.clone()

    if(options.rounding){
      position.x = Number(position.x.toFixed(options.rounding));
      position.y = Number(position.y.toFixed(options.rounding));
      position.z = Number(position.z.toFixed(options.rounding));
    }

    return position;

  }
  setTargetPosition(position: THREE.Vector3){
    return this.controls.target.copy(position)
  }
  enableControls(){
    this.controls.enabled = true;
  }
  disableControls(){
    this.controls.enabled = false;
  }
  setPolarAngle(min: number = 0, max: number = 1){
    this.controls.minPolarAngle = min * Math.PI;
    this.controls.maxPolarAngle = max * Math.PI;
  }
  resetPolarAngle(){
    this.setPolarAngle(0, 1)
  }

  onControlStart(event: any){
    this.controlling = true;
  }
  onControlEnd(event: any){
    this.controlling = false;
  }

  //Mouse Buttons
  setLeftButton(action: THREE.MOUSE | null){
    this.controls.mouseButtons.LEFT = action;
  }
  setMiddleButton(action: THREE.MOUSE | null){
    this.controls.mouseButtons.MIDDLE = action;
  }
  setRightButton(action: THREE.MOUSE | null){
    this.controls.mouseButtons.RIGHT = action;
  }

  //Mouse Touches
  setTouchesOne(action: any){
    this.controls.touches.ONE = action;
  }
  setTouchesTwo(action: any){
    this.controls.touches.TWO = action;
  }

  //Camera
  getCameraPosition(options: any = {}): Vector3{
    const position: Vector3 = this.camera.position.clone();

    if(options.rounding){
      position.x = Number(position.x.toFixed(options.rounding));
      position.y = Number(position.y.toFixed(options.rounding));
      position.z = Number(position.z.toFixed(options.rounding));
    }

    return position;
  }
  setCameraPosition(position: THREE.Vector3){
    return this.camera.position.copy(position)
  }

  //Lightning
  toggleLightning(){
    this.lightning.state = !this.lightning.state
  }
  changeLightningIntensity(value: any = null){
    if(value){
      this.lightning.intensity = value;
    }

    this.lightning.ambient.intensity = this.lightning.intensity
  }

  //Scene
  add(object: any) {
    this.scene.add(object);
  }
  remove(object: THREE.Object3D | null | undefined) {
    if (!object) { return; }

    object.traverse((child: THREE.Object3D) => {
      if(!(child instanceof THREE.Mesh)){ return; }

      let { geometry, material } = child;

      if(geometry){
        geometry.dispose();
      }
      if(material){
        if(!Array.isArray(material)){
          material = [ material ]
        }

        material.forEach((m: any) => {
          if(!m){ return; }

          if(typeof m.dispose === 'function'){
            m.dispose();
          }

          if (m.map && typeof m.map.dispose === 'function') {
            m.map.dispose();
          }
        });
      }

    });

    if(this.isTransformerAttached(object.uuid)){
      this.transform(object);
    }

    this.scene.remove(object);
  }
  resize(){
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
  }
  render(){
    this.controls.update();
    this.renderer.render(this.scene, this.camera);
  }

}
