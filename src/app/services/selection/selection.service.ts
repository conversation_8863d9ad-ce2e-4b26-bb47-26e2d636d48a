import {Injectable, ViewChild} from '@angular/core';
import {ModelsService} from "../models/models.service";
import * as THREE from "three";
import {SceneService} from "../scene/scene.service";
import {UiService} from "../ui/ui.service";
import {HelpersService} from "../helpers/helpers.service";
import { GLTFExporter } from 'three/examples/jsm/exporters/GLTFExporter.js';
import {Mesh, Vector3} from "three";
import {ModelSelection} from "../../models/model_selection/model-selection";
import {DatasetsService} from "../datasets/datasets.service";
import { SelectionTypeEnum as STE } from "../../enums/SelectionTypeEnums";
import {CalculationsService} from "../calculations/calculations.service";


@Injectable({
    providedIn: 'root',
})
export class SelectionService {

  public state: boolean = false;

  //Indicator
  public indicator: any = null;
  public indicator_color: string = '#fb8300'
  public indicator_highlight_color: string = '#FF5555'
  public indicator_radius: number = 0.1

  // Markers
  public selected_marker: any = null;
  public grabbed_marker: any = null;

  //Selections
  public polygon: any = null;
  public polygon_points: Vector3[] = [];
  public polygon_surface_area: number = 0;
  public polygon_circumference: number = 0;
  public markers: Mesh[] = [];

  //Actions
  //TODO unset
  public name: string = '';
  public type: string = STE.EMPTY;
  public dataset_id: number = 1;
  public dataset_row_id: number = 0;

  constructor(
      private helpers: HelpersService,
      private modelsService: ModelsService,
      private sceneService: SceneService,
      private uiService: UiService,
      private calculationsService: CalculationsService,
      private datasetsService: DatasetsService,
  ) {}


    toggleSelection(){
      this.state = !this.state;

      if(this.state){
        this.defaultValues();
        this.appendIndicator()
      }
      else{
        this.clear();
      }

    }
    defaultValues(){
      const index = this.modelsService.model_selections.length + 1;
      this.name = `Selectie ${index}`;

    }
    clear(){
      this.state = false;
      this.indicator_color = '#fb8300';

      this.removeIndicator();
      this.clearMarkers();
      this.clearPolygon();
      this.clearSelection();
    }

    //Selection
    select(event: MouseEvent) {
      if (!this.state || this.grabbed_marker) { return; }
      if(this.sceneService.controlling){
        this.indicator.visible = false;
        return;
      }

      const marker_intersection = this.calculationsService.getRaycastFromMouse(event, this.markers);
      if(marker_intersection){
        this.uiService.setCss('cursor', 'pointer');
        this.selected_marker = marker_intersection.object;
        this.indicator.visible = false;
        return;
      }
      else{
        this.selected_marker = null;
        this.uiService.setCss('cursor', 'default');
      }

      const intersection = this.calculationsService.getRaycastFromMouse(event, this.modelsService.getModelObjects());
      if (!intersection) {
        this.indicator.visible = false;
        return;
      }

      this.indicator.position.copy(intersection.point);
      this.indicator.visible = true;
    }
    selectNonCanvas(){
      if(!this.state){ return; }

      this.indicator.visible = false;
      this.uiService.setCss('cursor', 'default');
    }

    // markers
    confirmMarker(event: MouseEvent) {
      if(!this.state || this.selected_marker || event.button !== 0) { return; }
      if(this.polygon && this.sceneService.isTransformerAttached(this.polygon.uuid)){
        this.sceneService.transform(this.polygon);
      }

      const marker = this.indicator.clone();

      //Remove the indicator.material reference
      marker.visible = true;
      marker.material = marker.material.clone();
      marker.material.color.set(this.indicator_color);

      this.sceneService.add(marker);
      this.markers.push(marker);
      this.setPolygon();
    }
    clearMarkers(){
      for(const marker of this.markers){
        this.sceneService.remove(marker);
      }
      this.markers = [];
    }
    removeMarker(uuid: string){
      const marker = this.markers.find((marker: any) => marker.uuid === uuid);
      const index = this.markers.findIndex((marker: any) => marker.uuid === uuid);

      this.markers.splice(index, 1);
      this.sceneService.remove(marker);
    }

    highlightDeleteMarker(event: MouseEvent) {
      if(!this.state || !this.selected_marker || event.button !== 2){ return; }

      this.selected_marker.material.color.set(this.indicator_highlight_color);
    }
    deHighlightDeleteMarker(event: MouseEvent) {
      if(!this.state || event.button !== 2){ return; }

      if(this.selected_marker){
        this.removeMarker(this.selected_marker.uuid);
        this.setPolygon();
      }

      this.markers.forEach((marker: any) => {
        marker.material.color.set(this.indicator_color);
      })
    }
    grabMarker(event: MouseEvent) {
      if(!this.state || !this.selected_marker || event.button !== 0){ return; }
      this.grabbed_marker = this.selected_marker;
      this.sceneService.disableControls();
    }
    releaseMarker(event: MouseEvent) {
      if(!this.state || !this.grabbed_marker){ return; }
      this.grabbed_marker = null;
      this.sceneService.enableControls();
    }
    moveMarker(event: MouseEvent) {
      if(!this.state || !this.grabbed_marker){ return; }

      const original_position = this.grabbed_marker.position.clone();
      const intersection = this.calculationsService.getRaycastFromMouse(event, this.modelsService.getModelObjects());
      if (!intersection) {
        return;
      }

      this.grabbed_marker.position.copy(intersection.point);
      this.setPolygon({use_convex_hull: false});

      if(this.polygon && !this.isPolygonValid()){
        this.grabbed_marker.position.copy(original_position);
        this.setPolygon({use_convex_hull: false});
      }
    }

    // Polygon
    setPolygon(options: { use_convex_hull?: boolean } = {}) {
      this.clearPolygon();
      if(this.markers.length < 3){ return; }

      const default_options = { use_convex_hull: true };
      options = { ...default_options, ...options };

      const points = this.getPolygonPoints({
        use_convex_hull: options.use_convex_hull,
      });

      this.polygon = this.helpers.createPolygon(points, this.indicator_color);
      this.polygon_surface_area = this.calculationsService.calculateSurfaceArea(points);
      this.polygon_circumference = this.calculationsService.calculateCircumference(points, true);

      this.sceneService.add(this.polygon);
    }
    clearPolygon(){
      if(!this.polygon){ return; }

      this.sceneService.remove(this.polygon);
      this.polygon = null;
    }
    getPolygonPoints(options: { use_convex_hull?: boolean } = {}): Vector3[] {
      const default_options = { use_convex_hull: true };
      options = { ...default_options, ...options };

      const points = this.markers.map((marker: any) => marker.position);
      if(!options.use_convex_hull){ return this.polygon_points; }

      const centroid = new Vector3();
      points.forEach((point: any) => centroid.add(point));
      centroid.divideScalar(points.length);

      this.polygon_points = points.sort((a: any, b: any) => {
        const angleA = Math.atan2(a.z - centroid.z, a.x - centroid.x);
        const angleB = Math.atan2(b.z - centroid.z, b.x - centroid.x);
        return angleA - angleB;
      });

      return this.polygon_points;
    }
    isPolygonValid(): boolean {
      if (this.markers.length < 3) { return false; }

      const points = this.getPolygonPoints({ use_convex_hull: false });
      const numPoints = points.length;

      function segmentsIntersect(p1: Vector3, p2: Vector3, q1: Vector3, q2: Vector3): boolean {
        function cross(v1: Vector3, v2: Vector3): number {
          return v1.x * v2.z - v1.z * v2.x;
        }

        const r = new Vector3(p2.x - p1.x, 0, p2.z - p1.z);
        const s = new Vector3(q2.x - q1.x, 0, q2.z - q1.z);

        const numerator1 = cross(new Vector3(q1.x - p1.x, 0, q1.z - p1.z), r);
        const numerator2 = cross(new Vector3(q1.x - p1.x, 0, q1.z - p1.z), s);
        const denominator = cross(r, s);

        if (denominator === 0) return false;

        const t = numerator2 / denominator;
        const u = numerator1 / denominator;

        return t > 0 && t < 1 && u > 0 && u < 1;
      }

      for (let i = 0; i < numPoints; i++) {
        for (let j = i + 1; j < numPoints; j++) {
          const p1 = points[i];
          const p2 = points[(i + 1) % numPoints];
          const q1 = points[j];
          const q2 = points[(j + 1) % numPoints];

          if (Math.abs(i - j) === 1 || (i === 0 && j === numPoints - 1)) {
            continue;
          }

          if (segmentsIntersect(p1, p2, q1, q2)) {
            return false;
          }
        }
      }
      return true;
    }

    // Indicator
    appendIndicator(){
      this.indicator = this.helpers.createDot(this.indicator_color, this.indicator_radius);

      this.indicator.visible = false;
      this.sceneService.add(this.indicator);
    }
    removeIndicator(){
      this.sceneService.remove(this.indicator);
    }
    setIndicatorRadius(){
      this.removeIndicator();
      this.appendIndicator();
    }

    //selections
    async confirmSelection(){
      if(this.sceneService.isTransformerAttached( this.polygon.uuid )){
        this.sceneService.transform(this.polygon, this.markers);
      }


      const exporter = new GLTFExporter();
      exporter.parse(this.polygon, async (result: any) => {
        const blob = new Blob([JSON.stringify(result)], { type: 'application/octet-stream' });

        const post_data: any = {
          model_id: this.modelsService.model?.id,
          name: this.name,
          color: this.indicator_color,
          type: this.type,
          surface_area: this.polygon_surface_area,
          circumference: this.polygon_circumference,
        }
        if(this.type == STE.DATASET){
          post_data.dataset_id = this.dataset_id
          post_data.dataset_row_id = this.dataset_row_id
        }

        const { data } = await this.helpers.postFile('/api/editor/selection/store', post_data, [
            {key: 'polygon', data: blob, name: 'polygon.glb'}
        ]);

        const selection = new ModelSelection(data.selection);
        await this.modelsService.loadObject(selection);
        this.modelsService.toggleObjectVisibility(selection)
        this.modelsService.toggleObjectDepthTest(selection)

        this.clear();
      }, {
        // @ts-ignore
        binary: true
      });
    }
    deleteSelection(selection: ModelSelection){
      const remove = () => {
        this.helpers.post('/api/editor/selection/delete', { guid: selection.guid });
        this.sceneService.remove(selection.object);
        this.modelsService.model_selections = this.modelsService.model_selections.filter(
            (model_selection: ModelSelection) => model_selection.guid != selection.guid
        );
      }

      this.uiService.showConfirmModal({
        title: 'Selectie verwijderen',
        message: `Weet je zeker dat je de selectie "${selection.name}" wilt verwijderen?`,
        confirm_text: 'Verwijderen',
        onConfirm: remove
      });
    }
    clearSelection(){
      this.type = '';
      this.dataset_id = 0;
      this.dataset_row_id = 0;
    }
    isSelectionValid(){
      if(!this.polygon){ return false; }
      if(!this.type){ return false; }
      if(!this.name){ return false; }
      if(this.type == 'dataset' && (!this.dataset_id || !this.dataset_row_id)){ return false; }

      return true;
    }

    //Datasets(){
    datasetRowSelected(){
      const row = this.datasetsService.getRow(this.dataset_row_id);
      if(!row){ return; }

      const color = row.getValue('color');
      if(color){ this.setColor(color) }
    }

    //Color
    setColor(color: string = this.indicator_color){
      this.indicator_color = color;

      this.removeIndicator();
      this.appendIndicator();

      this.markers.forEach((marker: any) => {
        marker.material.color.set(color);
      })
      if(this.polygon){
        this.polygon.material.color.set(color);
      }
    }

}

