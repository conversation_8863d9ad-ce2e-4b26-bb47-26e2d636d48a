import {Injectable} from '@angular/core';
import {ModelsService} from "../models/models.service";
import * as THREE from "three";
import {SceneService} from "../scene/scene.service";


@Injectable({
    providedIn: 'root',
})
export class _SelectionService {

    public state: boolean = false;
    public dragging: boolean = false;

    public box_selector = {
        start: {x: 0, y: 0},
        end: {x: 0, y: 0},
        width: 0,
        height: 0,
    }

    constructor(
        private modelsService: ModelsService,
        private sceneService: SceneService
    ) {}

    toggleSelection(): void {
        this.state = !this.state;
        this.sceneService.controls.enabled = !this.state;

        if(!this.state){
            this.dragging = false;
        }
    }
    highlightVerticesInBox() {
        const {start, end} = this.box_selector

        const camera = this.sceneService.camera;
        const model = this.modelsService.model_objects[0].object;

        const canvas = this.sceneService.renderer.domElement; // Assuming you have a renderer
        const width = canvas.clientWidth;
        const height = canvas.clientHeight;

        const raycaster = new THREE.Raycaster();
        const mouse = new THREE.Vector2();

        model.traverse((node: any) => {
            if (node.isMesh) {
                const geometry = node.geometry;
                if (geometry.isBufferGeometry) {
                    const position = geometry.attributes.position;
                    if (!geometry.attributes.color) {
                        const colors = new Float32Array(position.count * 3);
                        for (let i = 0; i < colors.length; i++) {
                            colors[i] = 1.0;
                        }
                        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
                    }
                    geometry.attributes.color.needsUpdate = true;
                }

                if (!node.material.vertexColors) {
                    node.material = new THREE.MeshBasicMaterial({
                        vertexColors: true,
                        map: node.material.map,
                    });
                }
            }
        });

        for (let x = start.x; x <= end.x; x++) {
            for (let y = start.y; y <= end.y; y++) {
                mouse.x = (x / width) * 2 - 1;
                mouse.y = -(y / height) * 2 + 1;

                raycaster.setFromCamera(mouse, camera);

                const intersects = raycaster.intersectObject(model, true);
                if (intersects.length > 0) {
                    const intersect = intersects[0]; // Only take the closest intersection
                    // @ts-ignore
                    const geometry = intersect.object.geometry;

                    if (geometry && geometry.isBufferGeometry) {
                        const colorArray = geometry.attributes.color.array;

                        const face = intersect.face;
                        if (face) {
                            const vertexIndices = [face.a, face.b, face.c];

                            vertexIndices.forEach((index) => {
                                colorArray[index * 3] = 1.0;
                                colorArray[index * 3 + 1] = 0.0;
                                colorArray[index * 3 + 2] = 0.0;
                            });

                            geometry.attributes.color.needsUpdate = true;
                        }
                    }
                }
            }
        }
    }

    //Selection
    onMouseDown(event: MouseEvent){
        if(!this.state){ return; }

        this.dragging = true;
        this.box_selector.start.x = event.clientX;
        this.box_selector.start.y = event.clientY;
    }
    onMouseMove(event: MouseEvent){
        if(!this.dragging){ return; }

        this.box_selector.end.x = event.clientX;
        this.box_selector.end.y = event.clientY;

        const { start, end } = this.box_selector;
        this.box_selector.width = Math.abs(start.x - end.x);
        this.box_selector.height = Math.abs(start.y - end.y);
    }
    onMouseUp(event: MouseEvent){
        this.dragging = false;
        this.highlightVerticesInBox()
    }

}

