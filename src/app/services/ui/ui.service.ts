import {ChangeDetectorRef, Injectable} from '@angular/core';
import {HelpersService} from "../helpers/helpers.service";
import {InsideNavigationEnums as INE} from "../../enums/InsideNavigationEnums";
import {ActivatedRoute} from "@angular/router";

@Injectable({
  providedIn: 'root'
})
export class UiService {

  public show: boolean = true;

  public messages: any = [];
  public css: any = {
    cursor: 'default'
  }

  public guid: string|null = null;
  public mode: null | 'FULL' | 'LITE' | '2D LITE' | '2D FULL' | 'MOBILE' | '2D MOBILE' | 'THERMAL' = null;
  public perspective: INE = INE.ORBIT;
  public rendering = false;
  public loading = true;
  public render_distance = 2.5;
  public render_distance_basis = 0.25;
  public zoom_speed = 0.05;

  public toggles: any = {
    timeline: false,
    models: false,
    objects: false,
    floor_selections: false,
    dataset_selections: false,
    preview_position: false,
    preview_size: false,
    preview_upscale: false,
    preview_img: false,
    thermal_position: false,
    thermal_size: false,
    thermal_iframe: false,
    files: false,
    comments: false,
  }

  public confirm_modal = {
    show: false,
    large: false,
    title: '',
    message: '',
    confirm_text: '',
    confirm_color: '',
    cancel_text: '',
    onConfirm: () => {}
  };

  public modals: any = {
    mode: null,
  }

  constructor(
      private helpers: HelpersService,
  ) {
    this.initUi();
  }

  //Init
  init(guid: string|null){
    this.guid = guid;
    this.initMode();
  }
  initMode(){
    this.mode = this.helpers.cookie(`editor_view_mode_${this.guid}`);
    if(this.helpers._get('mode')){
      this.mode = this.helpers._get('mode');
      this.helpers._getRemove('mode');
    }
  }
  initUi(){
    if(this.helpers._get('ui') === 'false'){ this.show = false; }
  }

  //Modes
  is2D(){
    if(!this.mode){ return false; }

    return this.mode.slice(0, 2) == '2D';
  }
  setMode(mode: 'FULL' | 'LITE' | '2D LITE' | '2D FULL' | 'MOBILE' | '2D MOBILE' | 'THERMAL'){
    if(mode == this.mode){ return; }

    this.modals.mode = null;

    this.mode = mode;
    this.loading = true;
    this.helpers.cookieSet(`editor_view_mode_${this.guid}`, mode);

    location.reload();
  }
  askSetMode(mode: string){
    if(mode == this.mode){ return; }
    this.modals.mode = mode;
  }
  dismissAskSetMode(){
    this.modals.mode = null;
  }

  //Messages
  setMessage(id: string, message: string){
    const message_instance = this.messages.find((row : any) => row.id == id);
    if(message_instance){
      message_instance.message = message;
      return;
    }

    this.messages.push({
      id: id,
      message: message,
    })
  }
  setDownloadMessage(id: string, fetched: number, size: number) {
    const formatSize = (bytes: number): string => {
      if (bytes >= 1024 * 1024) {
        return `${(bytes / 1024 / 1024).toFixed(0)}MB`;
      }
      else {
        return `${(bytes / 1024).toFixed(0)}KB`;
      }
    };
    this.setMessage(id, `Downloading ${formatSize(fetched)} / ${formatSize(size)}`);
  }
  clearMessage(id: string){
    this.messages = this.messages.filter((row: any) => {
      return row.id != id;
    })
  }

  //CSS
  setCss(key: string, value: string|number){
    this.css[key] = value;
  }

  //Render
  setRenderBasis(distance: number){
    distance = Number(distance.toFixed(2));

    this.render_distance = distance
    this.render_distance_basis = distance;
  }

  //Perspective
  setPerspective(perspective: INE){
    this.perspective = perspective;
  }

  //UI
  toggle(key: string, delay: number = 0){
    setTimeout(() =>{
      this.toggles[key] = !this.toggles[key];
    }, delay);
  }

  //Confirm modal
  showConfirmModal(options: { title?: string, large?: boolean, message: string, confirm_text?: string, confirm_color?: string, cancel_text?: string, onConfirm?: () => void }) {
    console.log(options);

    this.confirm_modal = {
      show: true,
      large: (options.large !== undefined) ? options.large : false,
      title: options.title || 'Bevestigen',
      message: options.message || '',
      confirm_text: options.confirm_text || 'Bevestigen',
      confirm_color: options.confirm_color || 'danger',
      cancel_text: options.cancel_text || '',
      onConfirm: options.onConfirm || (() => {})
    };

    console.log(this.confirm_modal);

  }

  hideConfirmModal() {
    this.confirm_modal.show = false;
  }

}
