import { Injectable } from '@angular/core';
import {SceneService} from "../scene/scene.service";
import {HelpersService} from "../helpers/helpers.service";
import * as THREE from 'three';
import {MeasurementsService} from "../measurements/measurements.service";
import {ModelsService} from "../models/models.service";
import {ScreenSelectionV2, ScreenSelectionV3} from "../../types/ScreenSelection.types";
import {CalculationsService} from "../calculations/calculations.service";

@Injectable({
  providedIn: 'root'
})
export class ScreenSelectionService {

  public state: boolean = false;
  public dragging: boolean = false;
  public resolve: any = null;
  public reject: any = null;
  public type: 'V2' | 'V3' = 'V2';
  public objects: THREE.Object3D[] | THREE.Mesh[] | null = null;

  //Visuals
  public visible: boolean = true;
  public transparent_background: boolean = false;

  //DOM Selection
  public width: number = 0;
  public height: number = 0;

  public left: number = 0;
  public top: number = 0;

  public start: {x: number, y: number} = { x: 0, y: 0 }
  public end: {x: number, y: number} = { x: 0, y: 0 }

  //Callbacks
  public v2_callback: null | ((data: ScreenSelectionV2) => any) = null
  public v3_callback: null | ((data: ScreenSelectionV3) => any) = null

  //CSS
  public border_color: string = "#fb8300";
  public background_color: string = "#FB830059";

  constructor(
    private sceneService: SceneService,
    private modelsService: ModelsService,
    private calculationsService: CalculationsService,
  ) { }

  private async select(): Promise<any> {
    this.reset();
    this.state = true;

    return new Promise((resolve, reject) => {
      this.resolve = resolve;
      this.reject = reject;
      this.sceneService.disableControls()
    });
  }
  async selectV2(): Promise<ScreenSelectionV2>{
    this.type = 'V2';
    return this.select();
  }
  async selectV3(): Promise<ScreenSelectionV3>{
    this.type = 'V3';
    return this.select();
  }


  async confirm(event: MouseEvent){
    if(!this.state || !this.dragging || event.button !== 0){ return; }

    await this.setEnd(event);

    this.type == 'V2'
      ? this.resolve(this.getV2Data())
      : this.resolve(this.getV3Data())

    this.clear();
  }
  abort(){
    this.clear();

    if(typeof this.reject == 'function'){
      this.reject('Screen selection aborted');
    }

    this.reject = null;
    this.resolve = null;
  }
  clear(){
    this.state = false;
    this.width = 0;
    this.height = 0;
    this.left = 0;
    this.top = 0;
    this.start = { x: 0, y: 0 }
    this.end = { x: 0, y: 0 }
    this.dragging = false;
    this.border_color = "#fb8300";
    this.background_color = "#FB830059";
    this.objects = [];
    this.v2_callback = null;
    this.v3_callback = null;
    this.sceneService.enableControls();
  }

  getV2Data(): ScreenSelectionV2{
    return  {
      top_left: new THREE.Vector2( this.getTopLeft().x, this.getTopLeft().y ),
      top_right: new THREE.Vector2( this.getTopRight().x, this.getTopRight().y ),
      bottom_left: new THREE.Vector2( this.getBottomLeft().x, this.getBottomLeft().y ),
      bottom_right: new THREE.Vector2( this.getBottomRight().x, this.getBottomRight().y ),
    };
  }
  getV3Data(): ScreenSelectionV3{
    const objects = this.objects
      ? this.objects
      : this.modelsService.getModelObjects();

    const top_left_intersection = this.calculationsService.getRaycastFromScreen( this.getTopLeft(), objects );
    const top_right_intersection = this.calculationsService.getRaycastFromScreen( this.getTopRight(), objects );
    const bottom_left_intersection = this.calculationsService.getRaycastFromScreen( this.getBottomLeft(), objects );
    const bottom_right_intersection = this.calculationsService.getRaycastFromScreen( this.getBottomRight(), objects );
    const middle_intersection = this.calculationsService.getRaycastFromScreen( this.getMiddle(), objects );

    let hits = 0;
    for(const vector of [top_left_intersection, top_right_intersection, bottom_left_intersection, bottom_right_intersection]){
      if(vector){ hits++ }
    }

    return {
      top_left: top_left_intersection?.point || null,
      top_right: top_right_intersection?.point || null,
      bottom_left: bottom_left_intersection?.point || null,
      bottom_right: bottom_right_intersection?.point || null,
      middle: middle_intersection?.point || null,

      top_left_intersection,
      top_right_intersection,
      bottom_left_intersection,
      bottom_right_intersection,
      middle_intersection,

      hits
    };
  }

  reset(){
    this.top = 0;
    this.left = 0;
    this.width = 0;
    this.height = 0;
    this.start = {x: 0, y: 0};
    this.end = {x: 0, y: 0};
  }

  async setStart(event: MouseEvent){
    if(!this.state || event.button !== 0){ return; }

    this.dragging = true;

    this.start.x = event.pageX;
    this.start.y = event.pageY;
  }
  async setEnd(event: MouseEvent){
    if(!this.state || !this.dragging){ return; }

    this.end.x = event.pageX;
    this.end.y = event.pageY;

    this.width = Math.abs(this.start.x - this.end.x);
    this.height = Math.abs(this.start.y - this.end.y);

    await this.setPosition();
  }
  async setPosition(){
    this.left = this.start.x;
    this.top = this.start.y;

    if(this.end.y < this.start.y){
      this.top = this.end.y;
    }
    if(this.end.x < this.start.x){
      this.left = this.end.x;
    }

    if(this.startExists()){
      if(this.type == 'V2' && typeof this.v2_callback == 'function'){
        await this.v2_callback(this.getV2Data());
      }
      if(this.type == 'V3' && typeof this.v3_callback == 'function'){
        await this.v3_callback(this.getV3Data());
      }
    }
  }


  startExists(){
    return this.start.x && this.start.y
  }

  private getTopLeft(): THREE.Vector2{
    return new THREE.Vector2(
      Math.min(this.start.x, this.end.x),
      Math.min(this.start.y, this.end.y),
    );
  }
  private getTopRight(): THREE.Vector2{
    return new THREE.Vector2(
      Math.max(this.start.x, this.end.x),
      Math.min(this.start.y, this.end.y)
    );
  }
  private getBottomLeft(): THREE.Vector2{
    return new THREE.Vector2(
      Math.min(this.start.x, this.end.x),
      Math.max(this.start.y, this.end.y)
    );
  }
  private getBottomRight(): THREE.Vector2{
    return new THREE.Vector2(
      Math.max(this.start.x, this.end.x),
      Math.max(this.start.y, this.end.y)
    );
  }
  private getMiddle(): THREE.Vector2{
    return new THREE.Vector2(
      Math.round((Math.max(this.start.x, this.end.x) + Math.min(this.start.x, this.end.x)) / 2),
      Math.round((Math.max(this.start.y, this.end.y) + Math.min(this.start.y, this.end.y)) / 2),
    );
  }

}
