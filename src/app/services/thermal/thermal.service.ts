import { Injectable } from '@angular/core';
import {UiService} from "../ui/ui.service";
import {ModelsService} from "../models/models.service";
import {HelpersService} from "../helpers/helpers.service";
import {DomSanitizer, SafeResourceUrl} from '@angular/platform-browser';
import {SceneService} from "../scene/scene.service";

@Injectable({
  providedIn: 'root'
})
export class ThermalService {

  state: boolean = false;
  available: boolean = false;
  iframe_src: SafeResourceUrl = '';

  // Broadcast
  channel_freq: string = '';
  channel_id: string = '';
  channel: BroadcastChannel | undefined;


  constructor(
    protected uiService: UiService,
    protected modelsService: ModelsService,
    protected helpersService: HelpersService,
    protected sanitizer: DomSanitizer,
    protected sceneService: SceneService
  ) { }

  init(){
    this.setAvailable();
    this.setChannel();

    if(!this.available){ return; }

    this.setListeners();
    this.setIframeSrc();
  }
  toggle(){
    this.state = !this.state;

    this.uiService.toggle('thermal_position', this.state ? 0 : 200);
    this.uiService.toggle('thermal_size', this.state ? 200 : 0);
    this.uiService.toggle('thermal_iframe', 0);
    this.uiService.toggle('thermal_iframe', 800)

    if(!this.state){
      this.sendPosition();
    }

  }

  setAvailable(){
    if(!this.modelsService.model?._thermal){ return; }
    if(this.helpersService.isMobile() || this.helpersService._get('iframe')){ return; }
    if(this.uiService.is2D()){ return; }

    this.available = true;
  }
  setChannel(){
    this.channel_freq = this.helpersService._get('thermal_freq', this.helpersService.randomString(5));
    this.channel_id = this.helpersService.randomString()
    this.channel = new BroadcastChannel(`camera_target_vectors_${this.channel_freq}`);
    this.channel.onmessage = this.onMessage.bind(this);
  }
  setIframeSrc(){

    const data = {
      iframe: 'true',
      ui: 'false',
      mode: 'THERMAL',
      thermal_freq: this.channel_freq
    }

    this.iframe_src = this.sanitizer.bypassSecurityTrustResourceUrl(`${window.origin}/editor/${this.modelsService.model?.guid}?${new URLSearchParams(data).toString()}`);
  }
  setListeners(){
    this.sceneService.controls.addEventListener('change', this.sendPosition.bind(this));
  }

  sendPosition(){
    if(!this.channel){ return; }

    const data = {
      id: this.channel_id,
      camera: this.sceneService.getCameraPosition(),
      target: this.sceneService.getTargetPosition(),
    }

    this.channel.postMessage(data);
  }
  onMessage(event: MessageEvent){
    const { id, camera, target } = event.data;
    if(id === this.channel_id){ return; }

    this.sceneService.setCameraPosition(camera);
    this.sceneService.setTargetPosition(target);
  }

}
