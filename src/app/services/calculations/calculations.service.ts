import { Injectable } from '@angular/core';
import * as THREE from "three";
import earcut from "earcut";
import {SceneService} from "../scene/scene.service";
import {Mesh, Vector2, Vector3} from "three";
import {HelpersService} from "../helpers/helpers.service";
import {ModelsService} from "../models/models.service";
import {Decimal} from "decimal.js";

@Injectable({
  providedIn: 'root'
})
export class CalculationsService {

  private mouse = new THREE.Vector2();
  private raycaster = new THREE.Raycaster();

  constructor(
    private sceneService: SceneService,
    private helpers: HelpersService,
    private modelsService: ModelsService,
  ) { }

  getRaycastFromMouse(event: MouseEvent | Touch, objects: THREE.Object3D[]){
    if(!objects.length){ return null; }


    this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
    this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

    this.raycaster.setFromCamera(this.mouse, this.sceneService.camera);

    const intersects = this.raycaster.intersectObjects(objects, true);
    if(!intersects.length){ return null; }

    return intersects[0];
  }
  getRaycastFromScreen(origin: THREE.Vector2, objects: THREE.Object3D[]){
    if(!objects.length){ return null; }

    const normalized = new THREE.Vector2(
      (origin.x / window.innerWidth) * 2 - 1,
      -(origin.y / window.innerHeight) * 2 + 1
    )

    this.raycaster.setFromCamera(normalized, this.sceneService.camera);

    const intersects = this.raycaster.intersectObjects(objects, true);
    if(!intersects.length){ return null; }

    return intersects[0];
  }
  getRaycastFromOrigin(origin: THREE.Vector3, direction: THREE.Vector3, objects: THREE.Object3D[]){
    if(!objects.length){ return null; }

    const normalized_direction = direction.clone().normalize();
    this.raycaster.set(origin, normalized_direction);

    const intersects = this.raycaster.intersectObjects(objects, true);
    if(!intersects.length){ return null; }

    return intersects[0];
  }
  getRaycastFromOriginToTarget(origin: THREE.Vector3, target: THREE.Vector3, objects: THREE.Object3D[]){
    if(!objects.length){ return null; }

    const direction = target.clone().sub(origin).normalize();
    this.raycaster.set(origin, direction);

    const intersects = this.raycaster.intersectObjects(objects, true);
    if(!intersects.length){ return null; }

    return intersects[0];
  }

  getAverageFaceNormals(event: MouseEvent, objects: THREE.Object3D[]):{ left: Vector3, right: Vector3, forward: Vector3, backward: Vector3, up: Vector3, down: Vector3 } | null{
    let normal = new Vector3();
    let intersections = [];

    const center = this.getRaycastFromMouse(event, objects);
    if(!center){ return null; }

    const { clientX, clientY } = event;
    for(let x = (clientX - 40); x <= (clientX + 40); x += 2){
      for(let y = (clientY - 40); y <= (clientY + 40); y += 2){

        //Raycast
        const intersection = this.getRaycastFromScreen(new Vector2(x, y), objects);
        if(!intersection?.face?.normal){ continue; }

        //World matrix
        let face_normal = intersection.face.normal.clone().applyNormalMatrix(
          new THREE.Matrix3().getNormalMatrix(intersection.object.matrixWorld)
        );

        normal.add(face_normal);
        // /--Debug--
        //
        // const line = this.helpers.createLine(
        //   intersection.point.clone(),
        //   intersection.point.clone().add(face_normal.clone().negate()),
        //   '#FFFFFF',
        //   0.005
        // )
        // this.sceneService.add( line );
      }
    }

    normal.normalize().negate();

    const absolute_up = new Vector3(0, 1, 0);
    const left = new Vector3().crossVectors(absolute_up, normal).normalize();
    const right = left.clone().negate();
    const forward = new Vector3().crossVectors(right, normal).normalize();
    const backward = forward.clone().negate();
    const down = normal.clone();
    const up = down.clone().negate()

    return { left, right, forward, backward, up, down }
  }

  calculateCircumference(points: THREE.Vector3[], loop_back = false): number{
    if (points.length < 2) return 0;

    let total = 0;
    for(const i in points){
      const point = points[i];
      const next_point = points[Number(i) + 1];
      if(!next_point){
        if(loop_back){
          total += point.distanceTo( points[0] );
        }
        break;
      }

      total += point.distanceTo(next_point)
    }

    return Number(total.toFixed(2));
  }
  calculateSurfaceArea(points: THREE.Vector3[]): number {
    if (points.length < 3) return 0;

    const { vertices2D, normal } = this.projectTo2D(points);

    const indices = earcut(vertices2D);

    let totalArea = 0;
    for (let i = 0; i < indices.length; i += 3) {
      const i1 = indices[i];
      const i2 = indices[i + 1];
      const i3 = indices[i + 2];
      const v1 = points[i1];
      const v2 = points[i2];
      const v3 = points[i3];
      totalArea += this.calculateTriangleArea(v1, v2, v3);
    }

    return Number(totalArea.toFixed(2));
  }
  calculateTriangleArea(v1: THREE.Vector3, v2: THREE.Vector3, v3: THREE.Vector3): number {
    const edge1 = new THREE.Vector3().subVectors(v2, v1);
    const edge2 = new THREE.Vector3().subVectors(v3, v1);
    const crossProduct = new THREE.Vector3().crossVectors(edge1, edge2);
    return crossProduct.length() / 2;
  }
  projectTo2D(points: THREE.Vector3[]): { vertices2D: number[], normal: THREE.Vector3 } {
    const centroid = points.reduce((sum, point) => sum.add(point.clone()), new THREE.Vector3()).divideScalar(points.length);

    let normal = new THREE.Vector3();
    for (const point of points) {
      normal.add(point.clone().sub(centroid).cross(points[0].clone().sub(centroid)));
    }
    normal.normalize();

    const u = new THREE.Vector3(normal.y, normal.z, -normal.x).normalize();
    const v = new THREE.Vector3().crossVectors(normal, u).normalize();

    const vertices2D = points.flatMap(point => {
      const d = point.clone().sub(centroid);
      return [d.dot(u), d.dot(v)];
    });

    return { vertices2D, normal };
  }

  interpolatePosition(pos_1: THREE.Vector3, pos_2: THREE.Vector3, zoom: number){
    const distance = pos_1.distanceTo(pos_2);
    const direction = new THREE.Vector3().subVectors(pos_1, pos_2).normalize();

    const new_distance = distance * zoom;
    return pos_2.clone().add(direction.multiplyScalar(new_distance));
  }

  isPointInsideV2Polygon(point: Vector2, v2_polygon: Vector2[]): boolean{

    const v3_point = new Vector3(point.x, 1, point.y);
    const v3_polygon = v2_polygon.map((polygon_point: Vector2) => new Vector3(polygon_point.x, 0, polygon_point.y));

    const polygon = this.helpers.createPolygon(v3_polygon);
    polygon.visible = false
    this.sceneService.add(polygon);

    const intersection = this.getRaycastFromOrigin(
      v3_point,
      new Vector3(0, -1, 0),
      [ polygon ]
    );

    this.sceneService.remove(polygon);

    return !!intersection;
  }
  isPointInsideModel(point: Vector3, options: { threshold?: number, barrier_objects?: THREE.Mesh[], scan_objects?: THREE.Object3D[] } = {}){
    let objects = options.scan_objects
      ? options.scan_objects
      : this.modelsService.getModelObjects('', { include_maps: false });

    if(options.barrier_objects?.length){
      objects = objects.concat(options.barrier_objects);
    }

    let hits = 0;
    let distance = Infinity;
    let normal = null;
    let hit_point = null;

    const directions = [
      new Vector3(1, 0, 0), new Vector3(-1, 0, 0),
      new Vector3(0, 0, 1), new Vector3(0, 0, -1),

      new Vector3(-1, 0, -1), new Vector3(1, 0, -1),
      new Vector3(-1, 0, 1), new Vector3(1, 0, 1),

      new Vector3(1, 1, 0), new Vector3(-1, 1, 0),
      new Vector3(0, 1, 1), new Vector3(0, 1, -1),

      new Vector3(1, 2, 1), new Vector3(1, 2, -1),
      new Vector3(-1, 2, 1), new Vector3(1, 2, -1),
    ];
    const needed_hits = Math.ceil(directions.length * (options.threshold || .75));


    for (const dir of directions) {
      const intersection = this.getRaycastFromOrigin(point, dir.normalize(), objects);
      if (!intersection) { continue; }

        //Check if hit was on barrier, and the barrier was type model
        if(options.barrier_objects?.length){
          const barrier = options.barrier_objects.find((barrier: Mesh) => barrier.uuid == intersection.object.uuid);
          if(barrier && barrier?.userData['wall_type'] === 'barrier'){ continue; }
        }

        hits++;
        if (intersection.distance < distance) {
          distance = intersection.distance;
          normal = intersection.normal;
          hit_point = intersection.point;
        }
        if (hits >= needed_hits) break;

    }

    return {
      inside: hits >= needed_hits,
      certainty: new Decimal(hits).dividedBy(17).toDecimalPlaces(3).toNumber(),
      point: hit_point,
      normal: normal,
    };
  }

}
