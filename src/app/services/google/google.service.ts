import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class GoogleService {

  private API_KEY = "AIzaSyAOo20gZOetftvgtbu9NYFKt3-e1h7xajs";

  constructor() { }

  public sateliteImage(coordinates: string, options: any = {}){
    const zoom = options.zoom || 18;
    const size = options.size || 640;

    return `https://maps.googleapis.com/maps/api/staticmap?center=${coordinates}&zoom=${zoom}&size=${size}x${size}&maptype=satellite&key=${this.API_KEY}`
  }
  public areaToZoom(area: number){
    switch (area){
      case 30: return 21;
      case 60: return 20;
      case 120: return 19;
      case 240: return 18;
      case 480: return 17;
      case 960: return 16;
      default: return 17;
    }
  }

}
