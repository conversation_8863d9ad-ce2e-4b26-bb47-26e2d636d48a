import { Injectable } from '@angular/core';
import {HelpersService} from "../helpers/helpers.service";
import axios from "axios";
import {User} from "../../models/user/user.model";
import {ModelsService} from "../models/models.service";

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  token: string|null;

  public authenticated: boolean = false;
  public model_manageable: boolean = false;
  public model_customer_same_as_user: boolean = false;

  public user: User|undefined|null;
  private permissions: string[] = [];

  constructor(
      private helpers: HelpersService,
      private modelsService: ModelsService,
  ) {
    this.token = this.helpers.cookie('editor_token');
  }

  public async init(){
    await this.verifyAuth();
    await this.setCustomerSameAsUser();
    await this.setManageable();
  }

  public async verifyAuth() {
    const { data } = await this.helpers.post('/api/editor/verify-token', {
      token: this.token,
    });

    this.authenticated = data.authenticated;
    this.user = data.authenticated ? new User(data.user) : null;
    this.permissions = data.authenticated ? data.permissions : null;
  }
  public async setCustomerSameAsUser(){
    if(
      this.user?.customer_id &&
      this.modelsService.model?.customer_id &&
      this.user.customer_id === this.modelsService.model.customer_id
    ){
      this.model_customer_same_as_user = true;
    }
  }
  private async setManageable(){
    if(this.hasPermission('modellen_beheren')){
      this.model_manageable = true;
      return;
    }

    if(this.hasPermission('eigen_modellen_beheren') && this.model_customer_same_as_user){
      this.model_manageable = true;
      return;
    }

  }

  public login(token: string){
    this.helpers.cookieSet('editor_token', token)
    this.verifyAuth();
  }
  public logout(){
    this.helpers.cookieSet('editor_token', '');
    this.authenticated = false;
    this.user = null;
  }

  public hasPermission(key: string | string[]){
    if(!this.authenticated){ return false; }

    if(Array.isArray(key)){
      for(const permission of key){
        if(this.permissions.includes(permission)){ return true; }
      }

      return false;
    }

    return this.permissions.includes(key);
  }

}
