import {Injectable} from '@angular/core';
import {HelpersService} from "../helpers/helpers.service";

@Injectable({
    providedIn: 'root'
})
export class TutorialService {

    state = true;
    page = 1;
    pages = 7;

    constructor(
        private helpers: HelpersService,
    ) {
        this.state = this.helpers.cookie('editor_tutorial_seen') != 'true'
    }

    close() {
        this.state = false;
        this.helpers.cookieSet('editor_tutorial_seen', 'true');
    }
    open() {
        this.page = 1;
        this.state = true;
    }

    previousPage() {
        this.page += -1;
    }
    nextPage() {
        this.page++;
    }

}
