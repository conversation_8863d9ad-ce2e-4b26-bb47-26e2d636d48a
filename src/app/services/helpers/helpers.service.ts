import { Injectable } from '@angular/core';
import axios from "axios";

import * as THREE from "three";
// @ts-ignore
import { MeshLine, MeshLineMaterial } from 'three.meshline';
import { Vector3 } from "three";

@Injectable({
  providedIn: 'root'
})
export class HelpersService {

  public posting: boolean = false;
  public notification: any = {
    message: '',
    color: '',
  }
  public scrolled_to_bottom: any = {}

  constructor() { }

  // data
  _get(key: string | string[], def: any = null){
    const params = new URLSearchParams(window.location.search);

    if(Array.isArray(key)){
      const response = [];
      for(const param_key of key){
        response.push( params.get(param_key) ?? def )
      }

      return response;
    }

    return params.get(key) ?? def;
  }
  _getRemove(key: string){
    const url = new URL(window.location.href);
    url.searchParams.delete(key);
    window.history.replaceState({}, '', url);
  }

  // Strings
  copyString(val: string){
    const selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = val;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
  }
  randomString(length: number = 10) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    const charactersLength = characters.length;
    for ( let i = 0; i < length; i++ ) {
      result += characters.charAt(Math.floor(Math.random() *
        charactersLength));
    }

    return result;
  }

  // Cookies
  cookie(name: string, def: string|null = null): any {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) == ' '){
        c = c.substring(1);
      }
      if (c.indexOf(nameEQ) != -1){
        return c.substring(nameEQ.length, c.length);
      }
    }
    return def
  }
  cookieSet(name: string, value: string, days: number = 30) {
    const date = new Date();
    date.setTime(date.getTime() + (days * 24 * 3600 * 1000))
    document.cookie = `${name}=${value};expires=${date.toUTCString()};path=/`
  }

  // Requests
  async post(url: string, data: any = {}){
    this.posting = true;

    if(url.slice(0, 1) !== '/'){
      url = `/${url}`;
    }

    data['editor_token'] = this.cookie('editor_token');

    try{
      //TODO Change url
      // const response = await axios.post(`https://portal.dronemissions.eu${url}`, data);
      const response = await axios.post(`http://localhost:8000${url}`, data);
      this.posting = false;
      return response;
    }
    catch (err){
      this.posting = false;
      this.message('Er is iets foutgegaan!', 'danger');
      return { data: null };
    }

  }
  async postFile(url: string, data: any, files: any[] = []): Promise<any> {
    this.posting = true;

    try{
      const formData = new FormData();
      formData.append('editor_token', this.cookie('editor_token'));

      for(const key in data){
        formData.append(key, data[key]);
      }
      for(const file of files){
        formData.append(file.key, file.data, file.name);
      }

      const resposne = await axios.post(`https://portal.dronemissions.eu${url}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      this.posting = false;
      return resposne;
    }
    catch (err) {
      this.posting = false;
      this.message('Er is iets foutgegaan!', 'danger');
      return err;
    }

  }

  // Checks
  isMobile(): boolean {

    // @ts-ignore
    if(navigator.userAgentData?.mobile === false){ return false; }
    if(navigator.maxTouchPoints){ return true; }

    const userAgent = navigator.userAgent || navigator.vendor
    return /android|iPhone|iPad|iPod|opera mini|iemobile|windows phone|blackberry/i.test(userAgent);
  }
  isCanvas(event: MouseEvent | TouchEvent): boolean {
    const element = event.target as HTMLElement;
    return element.tagName.toLowerCase() === 'canvas';
  }

  // DOM
  sleep(ms: number){
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(null);
      }, ms);
    })
  }
  message(message: string, color: string = 'primary'){
    this.notification.message = message;
    this.notification.color = color;

    setTimeout(() => {
      this.notification.message = '';
    }, 2000);
  }
  scrolledToBottom(query: string){
    const element = document.querySelector(query);

    if (element) {
      const scrollTop = element.scrollTop;
      const scrollHeight = element.scrollHeight;
      const clientHeight = element.clientHeight;

      const scrolled = scrollTop + clientHeight >= scrollHeight;
      this.scrolled_to_bottom[query] = scrolled

      return scrolled;
    }

    return false;
  }
  redirect(url: string, blank: boolean = false){
    blank
      ? window.open(url, '_blank')
      : location.href = url;
  }
  redirectPortal(url: string, blank: boolean = false){
    if(url.slice(0, 1) == '/'){
      url = url.slice(1, url.length);
    }

    return this.redirect(`https://portal.dronemissions.eu/${url}`, blank);
  }

  // Objects
  getObjectCenter(object: THREE.Object3D): Vector3{
    const box = new THREE.Box3().setFromObject(object);
    const center = new THREE.Vector3();
    box.getCenter(center);

    return center.clone();
  }
  getObjectBoundingBox(object: THREE.Object3D | THREE.Object3D[]): THREE.Box3 {
    const box = new THREE.Box3();

    if (Array.isArray(object)) {
      for (const obj of object) {
        const object_box = new THREE.Box3().setFromObject(obj);
        box.union(object_box);
      }
    }
    else {
      box.setFromObject(object);
    }

    return box;
  }
  getObjectMaxDiagonalSize(object: THREE.Object3D): number{
    const { min, max } = this.getObjectBoundingBox(object);

    const from = new Vector3(min.x, min.y, min.z)
    const to = new Vector3(max.x, max.y, max.z)

    return from.distanceTo(to);
  }
  getObjectWorldPosition(object: THREE.Object3D): THREE.Vector3 {
    object.updateMatrixWorld(true);

    const position = new THREE.Vector3();
    object.getWorldPosition(position);
    return position;
  }
  getObjectGeometryWorldCenter(object: THREE.Mesh): THREE.Vector3 {
    const geometry = object.geometry as THREE.BufferGeometry;
    const posAttr = geometry.getAttribute('position');
    const center = new THREE.Vector3();

    for (let i = 0; i < posAttr.count; i++) {
      const vertex = new THREE.Vector3().fromBufferAttribute(posAttr, i);
      vertex.applyMatrix4(object.matrixWorld);
      center.add(vertex);
    }

    center.divideScalar(posAttr.count);
    return center;
  }
  getObjectBoxHelper(object: THREE.Object3D): THREE.Box3Helper{
    return new THREE.Box3Helper(this.getObjectBoundingBox(object), 0x00ff00);
  }

  interpolate(a: THREE.Vector3, b: THREE.Vector3, t: number): THREE.Vector3 {
    return new THREE.Vector3().lerpVectors(a, b, t);
  }
  subdivideMeshIntoGrid(mesh: THREE.Mesh, rows: number, cols: number, color: string = '#FFFFFF'): THREE.Group {
    const geometry = mesh.geometry as THREE.BufferGeometry;
    const positionAttr = geometry.getAttribute('position');

    if (!positionAttr || positionAttr.count < 4) {
      throw new Error('Mesh must have at least 4 vertices (2 triangles = rectangle)');
    }

    // Assume a rectangle defined by 2 triangles → 4 corner points
    const positions = [
      new THREE.Vector3().fromBufferAttribute(positionAttr, 0),
      new THREE.Vector3().fromBufferAttribute(positionAttr, 1),
      new THREE.Vector3().fromBufferAttribute(positionAttr, 2),
      new THREE.Vector3().fromBufferAttribute(positionAttr, 5), // 6th vertex = opposite corner
    ];

    const [p0, p1, p2, p3] = positions;
    const group = new THREE.Group();

    for (let row = 0; row < rows; row++) {
      const vStartLeft = this.interpolate(p0, p2, row / rows);
      const vEndLeft = this.interpolate(p0, p2, (row + 1) / rows);
      const vStartRight = this.interpolate(p1, p3, row / rows);
      const vEndRight = this.interpolate(p1, p3, (row + 1) / rows);

      for (let col = 0; col < cols; col++) {
        const topLeft = this.interpolate(vStartLeft, vStartRight, col / cols);
        const topRight = this.interpolate(vStartLeft, vStartRight, (col + 1) / cols);
        const bottomLeft = this.interpolate(vEndLeft, vEndRight, col / cols);
        const bottomRight = this.interpolate(vEndLeft, vEndRight, (col + 1) / cols);

        const vertices = [
          topLeft, bottomLeft, bottomRight,
          topLeft, bottomRight, topRight
        ];

        const flat = new Float32Array(vertices.flatMap(v => [v.x, v.y, v.z]));

        const subGeometry = new THREE.BufferGeometry();
        subGeometry.setAttribute('position', new THREE.BufferAttribute(flat, 3));
        subGeometry.computeVertexNormals();

        const material = new THREE.MeshBasicMaterial({ color, side: THREE.DoubleSide, transparent: true, opacity: 0.8 });
        const cellMesh = new THREE.Mesh(subGeometry, material);
        group.add(cellMesh);
      }
    }

    return group;
  }

  //Debug


  //Date
  parseDate(target: string | null = null) {
    const d = target ? new Date(target) : new Date();

    const time = `0${d.getHours()}`.slice(-2) + ':' + `0${d.getMinutes()}`.slice(-2);
    const date: any = {
      us: +d.getFullYear() + '-' + `0${d.getMonth() + 1}`.slice(-2) + '-' + `0${d.getDate()}`.slice(-2),
      eu: `0${d.getDate()}`.slice(-2) + '-' + `0${d.getMonth() + 1}`.slice(-2) + '-' + d.getFullYear(),
    };
    date.timestamp = new Date(date.us).getTime();

    const year = d.getFullYear();
    const month = d.getMonth() + 1;
    const day = d.getDate();
    const dayOfWeek = d.getDay();

    return {
      time: time,
      timestamp: d.getTime(),
      date: date,
      year: year,
      month: month,
      month_name: this.getMaanden(month),
      month_short_name: this.getShortMaanden(month),
      day: day,
      day_full: `0${day}`.slice(-2),
      dayOfWeek: dayOfWeek,
    };
  }
  getMaanden(maand: number | null = null){
    const maanden: any = {};
    maanden[1] = "Januari";
    maanden[2] = "Februari";
    maanden[3] = "Maart";
    maanden[4] = "April";
    maanden[5] = "Mei";
    maanden[6] = "Juni";
    maanden[7] = "Juli";
    maanden[8] = "Augustus";
    maanden[9] = "September";
    maanden[10] = "Oktober";
    maanden[11] = "November";
    maanden[12] = "December";
    if(maand !== null){
      return maanden[maand] ?? '';
    }
    return maanden;
  }
  getShortMaanden(maand: number | null = null){
    const maanden: any = {};
    maanden[1] = "Jan";
    maanden[2] = "Feb";
    maanden[3] = "Maa";
    maanden[4] = "Apr";
    maanden[5] = "Mei";
    maanden[6] = "Jun";
    maanden[7] = "Jul";
    maanden[8] = "Aug";
    maanden[9] = "Sep";
    maanden[10] = "Okt";
    maanden[11] = "Nov";
    maanden[12] = "Dec";
    if(maand !== null){
      return maanden[maand] ?? '';
    }
    return maanden;
  }
  msToTime(ms: number): string {
    const totalSeconds = Math.floor(ms / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    const paddedMinutes = String(minutes).padStart(2, '0');
    const paddedSeconds = String(seconds).padStart(2, '0');

    return `${paddedMinutes}:${paddedSeconds}`;
  }

  //Arrays
  uniqueArrFn(value: any, index: any, array: any) {
    return array.indexOf(value) === index;
  }
  sumArr(arr: number[], rounding: number | null = null){
    let tot = 0;
    for(const row of arr ){ tot += Number(row); }

    if(rounding !== null){
      tot = Number(tot.toFixed(rounding))
    }

    return tot;
  }
  averageArr(arr: number[], rounding: number | null = null){
    let tot = this.sumArr(arr);
    let average = tot / arr.length

    if(rounding !== null){
      average = Number(average.toFixed(rounding))
    }


    return average;
  }

  //Models
  createDot(color: string = '#FFFFFF', radius: number = 0.05, opacity: number = .75): THREE.Mesh {
    const dot_geometry = new THREE.SphereGeometry(radius, 20, 40);
    const core_geometry = new THREE.SphereGeometry(radius * 0.66, 20, 40);

    const dot_material = new THREE.MeshBasicMaterial({
      color: new THREE.Color(color),
      opacity,
      transparent: true,
      depthTest: false,
    });

    const core_material = new THREE.MeshBasicMaterial({
      color: new THREE.Color('#ffffff'),
      side: THREE.BackSide,
      depthTest: false,
      transparent: true,
      opacity: 1
    });

    const dot = new THREE.Mesh(dot_geometry, dot_material);
    const core = new THREE.Mesh(core_geometry, core_material);

    core.renderOrder = 9999;
    dot.renderOrder = 999;

    dot.add(core);

    return dot;
  }
  createCylinder(color: string = '#FFFFFF', radius: number = 0.05, height: number = 10, opacity: number = 1){
    const marker = new THREE.Mesh(
        new THREE.CylinderGeometry(radius, radius, height, 64),
        new THREE.MeshBasicMaterial({
          color: new THREE.Color(color),
          opacity: opacity,
          transparent: true,
          depthWrite: false,
        })

    );

    marker.renderOrder = 999;
    return marker;
  }
  createLine(pos_1: THREE.Vector3, pos_2: THREE.Vector3, color: string = '#FFFFFF', thickness: number = 0.05): THREE.Mesh{
    return this.createPipe([pos_1, pos_2], color, thickness)
  }
  createLineTube(pos_1: THREE.Vector3, pos_2: THREE.Vector3, color: string = '#FFFFFF', thickness: number = 0.05): THREE.Mesh{
    const curve = new THREE.LineCurve3(pos_1, pos_2);
    const geometry = new THREE.TubeGeometry(curve, 8, thickness, 8, false);

    const material = new THREE.MeshBasicMaterial({
      color: color,
      transparent: true,
      opacity: 0.75,
      depthTest: false,
      depthWrite: false,
      side: THREE.DoubleSide,
    });

    const mesh = new THREE.Mesh(geometry, material);
    mesh.renderOrder = 999;

    return mesh;
  }
  createPipe(points: THREE.Vector3[], color: string = '#FFFFFF', thickness: number = 0.05): THREE.Mesh{
    const geometry = new THREE.BufferGeometry().setFromPoints(points);

    const meshLine = new MeshLine();
    meshLine.setGeometry(geometry);

    const material = new MeshLineMaterial({
      color: color,
      lineWidth: thickness,
      transparent: true,
      opacity: .75,
      depthTest: false,
      depthWrite: false,
    });

    const mesh = new THREE.Mesh(
      meshLine.geometry,
      material
    )

    mesh.renderOrder = 999;

    return mesh;
  }
  createSprite(url: string, options: any = {}){
    const depth_test = options.depthTest || false;

    return new Promise(resolve => {
      const textureLoader = new THREE.TextureLoader();
      textureLoader.load(url, (texture) => {
        const spriteMaterial = new THREE.SpriteMaterial({ map: texture, depthTest: depth_test, });
        resolve(new THREE.Sprite(spriteMaterial))
      });

    })

  }
  createPlane(color: string, width: number, height: number){
    const plane_geometry = new THREE.PlaneGeometry(width, height);
    const plane  = new THREE.MeshBasicMaterial({
      color: color,
      opacity: .5,
      transparent: true,
      depthTest: false,
      depthWrite: false,
      side: THREE.DoubleSide,
    });

    const mesh = new THREE.Mesh(plane_geometry, plane);

    mesh.rotation.x = -Math.PI / 2;

    return mesh
  }
  createBlock(color: string, size: number): THREE.Mesh{
    const box_geometry = new THREE.BoxGeometry(size, size, size);
    const box  = new THREE.MeshBasicMaterial({
      color: color,
      opacity: .5,
      transparent: true,
      depthTest: false,
      depthWrite: false,
      side: THREE.DoubleSide,
    });

    const mesh = new THREE.Mesh(box_geometry, box);
    mesh.renderOrder = 999;
    return mesh
  }
  createInstancedBlock(color: string, size: number, count: number): THREE.InstancedMesh{
    const box_geometry = new THREE.BoxGeometry(size, size, size);
    const box  = new THREE.MeshBasicMaterial({
      color: color,
      opacity: .5,
      transparent: true,
      depthTest: false,
      depthWrite: false,
      side: THREE.DoubleSide,
    });

    const mesh = new THREE.InstancedMesh(box_geometry, box, count);
    mesh.instanceMatrix.setUsage(THREE.DynamicDrawUsage);
    mesh.renderOrder = 999;

    return mesh
  }
  createCirclePlane(color: string, radius: number){
    const plane_geometry = new THREE.CircleGeometry( radius, 32 );
    const plane  = new THREE.MeshBasicMaterial({
      color: color,
      opacity: .5,
      transparent: true,
      depthTest: false,
      depthWrite: false,
      side: THREE.DoubleSide,
    });

    return new THREE.Mesh(plane_geometry, plane);
  }
  createTexturedPlane(url: string, width: number, height: number){
    return new Promise(resolve => {
      const textureLoader = new THREE.TextureLoader();
      const plane_geometry = new THREE.PlaneGeometry(width, height);

      textureLoader.load(url, (texture) => {
        const plane  = new THREE.MeshBasicMaterial({
          map: texture,
          transparent: true,
        });
        const mesh = new THREE.Mesh(plane_geometry, plane);

        mesh.rotation.x = -Math.PI / 2;

        resolve(mesh);
      });
    })
  }
  createMap(data: Uint8Array, options = {}){
    return new Promise(resolve => {

      const blob = new Blob([data], { type: 'image/webp' });
      const url = URL.createObjectURL(blob);

      const textureLoader = new THREE.TextureLoader();
      textureLoader.load(url, (texture) => {

        const material = new THREE.MeshLambertMaterial({ map: texture });
        const geometry = new THREE.PlaneGeometry(100, 100);
        const plane = new THREE.Mesh(geometry, material);

        plane.rotation.x = -Math.PI / 2;

        URL.revokeObjectURL(url);

        resolve(plane)

      },
      undefined,
      (err: any) => {
        alert('Er is iets foutgegaan!');
      })

    })
  }
  createPolygon(points: Vector3[], color: string = '#FFFFFF', options: {renderOrder?: number, opacity?: number} = { renderOrder: 500, opacity: .5 }){
    const normal = new THREE.Vector3()
      .subVectors(points[1], points[0])
      .cross(new THREE.Vector3().subVectors(points[2], points[0]))
      .normalize();

    const xAxis = new THREE.Vector3().subVectors(points[1], points[0]).normalize();
    const yAxis = new THREE.Vector3().crossVectors(normal, xAxis).normalize();

    const projected2D = points.map(p => {
      const relative = new THREE.Vector3().subVectors(p, points[0]);
      return new THREE.Vector2(
        relative.dot(xAxis),
        relative.dot(yAxis)
      );
    });

    const indices = THREE.ShapeUtils.triangulateShape(projected2D, []);

    const vertices = points.map(p => [p.x, p.y, p.z]).flat();
    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.setIndex(indices.flat());
    geometry.computeVertexNormals();

    const material = new THREE.MeshBasicMaterial({
      color: new THREE.Color(color),
      transparent: true,
      opacity: options.opacity || .5,
      side: THREE.DoubleSide,
      depthTest: false,
    });

    const mesh = new THREE.Mesh(geometry, material);
    mesh.renderOrder = options.renderOrder || 999;

    return mesh;
  }
  createFromBuffer(vertices: number[], color: string = '#FFFFFF'): THREE.Mesh{
    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    geometry.computeVertexNormals();

    const material = new THREE.MeshBasicMaterial({
      color: color,
      side: THREE.DoubleSide,
      transparent: true,
      opacity: 0.5,
      depthTest: false,
      depthWrite: false,
    });

    const mesh = new THREE.Mesh(geometry, material);

    mesh.renderOrder = 999;

    return mesh;
  }

}
