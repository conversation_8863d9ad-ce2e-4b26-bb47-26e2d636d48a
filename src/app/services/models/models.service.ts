import {Injectable} from '@angular/core';
import axios from "axios";
import {Model} from "../../models/model/model.model";
import {SceneService} from "../scene/scene.service";
import {ModelObject} from "../../models/model_object/model-object.model";
import * as THREE from "three";
import {GLTFLoader} from "three/examples/jsm/loaders/GLTFLoader.js";
import {UiService} from "../ui/ui.service";
import {GoogleService} from "../google/google.service";
import * as MeshOpt from 'meshoptimizer';
import {KTX2Loader} from "three/examples/jsm/loaders/KTX2Loader.js";
import {HelpersService} from "../helpers/helpers.service";
import {ModelSelection} from "../../models/model_selection/model-selection";
import {Location} from "../../models/location/location.model";
import {File} from "../../models/file/file.model";
import {Vector2, Vector3} from "three";
import { acceleratedRaycast, computeBoundsTree, disposeBoundsTree } from 'three-mesh-bvh';


@Injectable({
  providedIn: 'root'
})
export class ModelsService {

  //Models
  public model: Model | undefined | null;
  public model_objects: ModelObject[] = [];
  public model_selections: ModelSelection[] = [];

  public address_models: Model[] = [];
  public files: File[] = [];

  public location: Location;

  public objects: any[] = [];
  public sprites: any = {};


  //Loading
  public loading_manager = new THREE.LoadingManager();
  public loader: any;

  constructor(
      private googleService: GoogleService,
      private sceneService: SceneService,
      private uiService: UiService,
      private helpersService: HelpersService,
  ) {
    this.location = new Location();
    THREE.Mesh.prototype.raycast = acceleratedRaycast;
  }

  public async setModel(guid: string | null){
    try{

      //Fetch Model
      const { data } = await this.helpersService.post(`/api/editor/model`, {
        guid: guid,
      })
      const { model, address_models, files } = data;

      this.model = new Model(model);
      this.address_models = address_models.map((model: any) => new Model(model));
      this.files = files.map((file: any) => new File(file));
      this.location = new Location(this.model.coordinates);

      return true;
    }
    catch (err){
      console.log(err);
      alert('Er is iets fout gegaan!');
      this.uiService.loading = false;
      return false;
    }
  }
  openModel(guid: string){
    const model = this.address_models.find((model: Model) => model.guid == guid);
    if(!model){ return; }

    const mode = this.uiService.mode
    const maps = this.getSpritePosition('google_maps');
    const data: any = {
      'title': model.name,
      'mode': mode,
    }

    if(this.location.active && maps){
      const camera_position = this.sceneService.getCameraPosition();
      const { lat: camera_lat, lon: camera_lon  } = this.location.calculateLocation( camera_position, maps );
      data.camera_lat = camera_lat;
      data.camera_lon = camera_lon;
      data.camera_maps_relative_y = this.sceneService.getCameraPosition().sub( maps ).y


      const target_position = this.sceneService.getTargetPosition();
      const { lat: target_lat, lon: target_lon  } = this.location.calculateLocation( target_position, maps );
      data.target_lat = target_lat;
      data.target_lon = target_lon;
      data.target_maps_relative_y = this.sceneService.getTargetPosition().sub( maps ).y
    }

    window.location.href = `${window.origin}/editor/${model.guid}?${new URLSearchParams(data).toString()}`;
  }

  //3D
  public async loadModel(){
    if(!this.model){ return; }

    const { mode } = this.uiService;

    if(mode == 'MOBILE'){
      this.model.objects = this.model.objects.filter((object: ModelObject) => object.quality == 'mobile');
    }
    else if(mode == 'LITE'){
      this.model.objects = this.model.objects.filter((object: ModelObject) => object.quality == 'low');
    }
    else if(mode == 'FULL'){
      this.model.objects = this.model.objects.filter((object: ModelObject) => object.quality == 'low' || object.quality == 'high');
    }

    else if(mode == '2D MOBILE'){
      this.model.objects = this.model.objects.filter((object: ModelObject) => object.quality == '2D_mobile');
    }
    else if(mode == '2D LITE'){
      this.model.objects = this.model.objects.filter((object: ModelObject) => object.quality == '2D_low');
    }
    else if(mode == '2D FULL'){
      this.model.objects = this.model.objects.filter((object: ModelObject) => object.quality == '2D_low' || object.quality == '2D_high');
    }
    else if(mode == 'THERMAL'){
      this.model.objects = this.model.objects.filter((object: ModelObject) => object.quality == 'thermal');
    }



    for(const object of this.model.objects){
      await this.loadObject(object);
    }
    this.calculateRenderDistanceBasis()
  }
  async loadSelections(){
    if(!this.model){ return; }

    for(const selection of this.model.selections){
      await this.loadObject(selection);
    }
  }
  getModelObjects(quality?: string, options: {include_maps?: boolean} = { include_maps: true }): THREE.Object3D[]{
    quality = this.getModelQuality(quality);

    const models: THREE.Object3D[] = [];
    if(this.sprites.google_maps && options?.include_maps){
      models.push(this.sprites.google_maps);
    }

    const model = this.model_objects.find((obj: any) => obj.quality == quality);
    if(model){
      models.push(model.object);
    }

    return models;
  }
  getModelObject(quality?: string): THREE.Object3D{
    quality = this.getModelQuality(quality);

    const model = this.model_objects.find((obj: any) => obj.quality == quality);
    return model?.object;
  }
  getModelQuality(quality?: string){
    if(quality){ return quality }

    if(this.uiService.mode == 'THERMAL'){
      quality = 'thermal';
    }
    else if(this.helpersService.isMobile()){
      quality = this.uiService.is2D() ? '2D_mobile' : 'mobile';
    }
    else{
      quality = this.uiService.is2D() ? '2D_low' : 'low';
    }

    return quality;
  }
  getModelBoundingBox(){
    return this.helpersService.getObjectBoundingBox(this.model_objects[0].object)
  }
  getModelMaxDiagonalSize(){
    return this.helpersService.getObjectMaxDiagonalSize( this.model_objects[0].object );
  }
  getModelCenter(): Vector3{
    const center = new Vector3();
    this.getModelBoundingBox().getCenter(center);

    return center;
  }
  getModelSize(): Vector3{
    const size = new Vector3();
    this.getModelBoundingBox().getSize(size);

    return size;
  }

  //Satelite
  loadSateliteView(options: any = {}){
    if(!this.location.active || !this.uiService.show){ return; }


    const area = options?.area || 240;
    const zoom = this.googleService.areaToZoom(area);

    return new Promise(async resolve => {
      const url = this.googleService.sateliteImage(this.location.coordinates, {zoom: zoom});

      this.sprites.google_maps = await this.helpersService.createTexturedPlane(url, area, area);
      this.sprites.google_maps.position.y = -30;
      this.sprites.google_maps.name = 'Google Maps'
      this.sprites.google_maps.area = area

      this.objects.push(this.sprites.google_maps);
      this.sceneService.add(this.sprites.google_maps);
      resolve(true);
    })

  }
  updateSateliteViewPosition(object: THREE.Mesh){
    if(!this.sprites.google_maps){ return; }

    const bounding_box = new THREE.Box3().setFromObject(object);
    this.sprites.google_maps.position.y = bounding_box.min.y;
  }
  async updateSateliteArea(area: number){
    const sprite = this.sprites.google_maps;
    const id = sprite.id;
    const position = sprite.position.clone();

    this.clearSprite('google_maps');
    await this.loadSateliteView({ area: area });

    this.sprites.google_maps.position.copy(position);
    this.objects = this.objects.filter(object => object.id != id )

  }

  //Download
  download(options: any): Promise<Uint8Array>{
    const url = options.url;
    const size = options.size;
    const id = options.id;

    return new Promise(async resolve => {
      const fetch_size = 1024 * 1024 * 100;
      let fetched_size = 0;
      let file = new Uint8Array(size);

      this.uiService.setDownloadMessage(id, fetched_size, size);
      while (fetched_size < size) {
        const end = Math.min(fetched_size + fetch_size - 1, size - 1);

        const { data } = await axios.get(url, {
          headers: { 'Range': `bytes=${fetched_size}-${end}` },
          responseType: 'arraybuffer',
          onDownloadProgress: (progressEvent) => {
            this.uiService.setDownloadMessage(id, (fetched_size + progressEvent.loaded), size);
          }
        });

        file.set(new Uint8Array(data), fetched_size);
        fetched_size += data.byteLength;
      }

      resolve(file);
    })
  }

  // LOD
  runLod() {
    if(!this.uiService.rendering || this.model_objects.length < 2){ return; }

    for(const model_object of this.model_objects){
      const { object, quality } = model_object;

      for(const child of object.children){
        this.runChildLod(child, quality);
      }
    }
  }
  runChildLod(child: any, quality: string){

    const { render_distance } = this.uiService;
    const bounding_box = new THREE.Box3();
    bounding_box.setFromObject(child);


    const closestPoint = bounding_box.clampPoint(this.sceneService.getCameraPosition(), new THREE.Vector3());
    const distance = Math.abs(this.sceneService.getCameraPosition().distanceTo(closestPoint));
    const visible = (quality == 'low' || quality == '2D_low')
        ? distance >= render_distance
        : distance <= render_distance * 2;

    if(child instanceof THREE.Mesh){
      child.visible = visible;
    }
    else if (child instanceof THREE.Group) {
      child.children.forEach((sub_child: any) => {
        if (sub_child instanceof THREE.Mesh) {
          sub_child.visible = visible;
        }
      });
      child.visible = visible;
    }
  }
  calculateRenderDistanceBasis(){

    let count = 0;
    let width = 0;
    let length = 0;

    for(const model_object of this.model_objects){
      const { object } = model_object;
      const { children } = object;

      for(const child of children){
        const box = new THREE.Box3();
        box.setFromObject( child );

        // const boxHelper = new THREE.Box3Helper(box, 0xffff00);
        // this.sceneService.add(boxHelper)

        width += box.max.x - box.min.x;
        length += box.max.z - box.min.z;
        count++;
      }
    }

    width = width / count;
    length = length / count;

    const distance = (width + length) / 2
    this.uiService.setRenderBasis(distance);
  }

  //Objects
  async loadObject(object: ModelObject | ModelSelection){
    const { size } = object;
    let identifier, route;

    //Define type specific params
    if(object instanceof ModelObject){
      const { guid } = object;

      identifier = guid;
      route = `api/models/content/${guid}`
    }
    else{
      const { guid } = object;

      identifier = guid;
      route = `file/models/selections/${guid}.glb`;
    }

    //Set loading listeners
    this.loading_manager.onProgress = (url: any, itemsLoaded: any, itemsTotal: any) => {
      this.uiService.setMessage(identifier, `Loading objects ${itemsLoaded} / ${itemsTotal}`)
    }
    this.loading_manager.onLoad = () => {
      this.uiService.clearMessage(identifier);
    }

    //TODO Change url
    let model_data: any = await this.download({
      // url: `https://portal.dronemissions.eu/${route}`,
      url: `http://localhost:8000/${route}`,
      size: size,
      id: identifier,
    });

    this.loader = new GLTFLoader(this.loading_manager);
    this.loader.setMeshoptDecoder(MeshOpt.MeshoptDecoder);

    const ktx_loader = new KTX2Loader();
    ktx_loader.setTranscoderPath('three/ktx/');
    ktx_loader.detectSupport(this.sceneService.renderer);
    this.loader.setKTX2Loader(ktx_loader);

    return new Promise(resolve => {
      this.loader.parse(
          model_data.buffer,
          '',
          (gltf: any) => {
            object.object = gltf.scene;
            model_data = null;

            this.precomputeBVH(object.object);
            this.sceneService.add(object.object);

            //Populate corresponding containers
            if(object instanceof ModelObject){
              this.model_objects.push(object);
              this.updateSateliteViewPosition(object.object);
            }
            else if(object instanceof ModelSelection){
              object.object.visible = false;
              this.model_selections.push(object);
            }

            resolve(true);
          },
          () => {},
      );

    })

  }
  toggleObjectVisibility(object: ModelObject | ModelSelection | THREE.Mesh, state: boolean | null = null){
    if(object instanceof THREE.Mesh){
      object.visible = !object.visible;
      return;
    }

    if(state === null){
      state = !object.object.visible
    }

    object.object.visible = state;
  }
  toggleObjectDepthTest(object: ModelSelection | THREE.Mesh, state: boolean | null = null){
    if(object instanceof THREE.Mesh){
      // @ts-ignore
      const state = !object.material.depthTest
      this.setObjectDepthTest(object, state);
      return;
    }

    if(state === null){
      state = !object.depth_test
    }

    const mesh = object.object;
    object.depth_test = state;

    this.setObjectDepthTest(mesh, state);
  }
  setObjectDepthTest(object: THREE.Mesh | THREE.Group, state: boolean){
    if(object instanceof THREE.Group){
      object.children.forEach((child: any) => { this.setObjectDepthTest(child, state); } );
      return;
    }

    // @ts-ignore
    object.material.depthTest = state;
  }

  //Selections
  getSelections(type: string): ModelSelection[]{
    return this.model_selections.filter((selection: ModelSelection) => selection.type == type)
  }
  getSelectionsObjects(type: string): THREE.Object3D[]{
    const selections =  this.model_selections.filter((selection: ModelSelection) => selection.type == type)
    const objects = selections.map((selection: ModelSelection) => selection.object);

    return objects;
  }
  getSelectionsGroups(group_key: keyof ModelSelection){
    return this.model_selections
        .filter((selection: ModelSelection) => !!selection[group_key])
        .map((selection: ModelSelection) => selection[group_key])
        .filter(this.helpersService.uniqueArrFn);
  }
  getSelectionsByGroup(group_key: keyof ModelSelection, value: any){
    return this.model_selections.filter((selection: ModelSelection) => selection[group_key] === value);
  }
  getSelectionsGroupSurfaceArea(group_key: keyof ModelSelection, value: any){
    const surface_areas = this.getSelectionsByGroup(group_key, value).map((selection: ModelSelection) => selection.surface_area);
    return this.helpersService.sumArr( surface_areas, 2)
  }
  isObjectSelectionType(object: any, type: string): boolean{
    if(object?.parent instanceof THREE.Group){ return this.isObjectSelectionType(object.parent, type); }

    const selection = this.model_selections.find(selection => selection.object.uuid == object.uuid );
    return selection?.type == type;
  }

  selectionGroupIsVisible(group_key: keyof ModelSelection, value: any){
    const selections = this.getSelectionsByGroup(group_key, value);
    for(const selection of selections){
      if(!selection.object.visible){ return false; }
    }

    return true;
  }
  selectionGroupHasDepthTest(group_key: keyof ModelSelection, value: any){
    const selections = this.getSelectionsByGroup(group_key, value);
    for(const selection of selections){
      if(!selection.depth_test){ return false; }
    }

    return true;
  }

  toggleSelectionGroupVisibility(group_key: keyof ModelSelection, value: any, state: boolean | null = null){
    this.getSelectionsByGroup(group_key, value).forEach((selection: ModelSelection) => this.toggleObjectVisibility(selection, state));
  }
  toggleSelectionGroupDepthTest(group_key: keyof ModelSelection, value: any, state: boolean | null = null){
    this.getSelectionsByGroup(group_key, value).forEach((selection: ModelSelection) => this.toggleObjectDepthTest(selection, state));
  }

  //Sprites
  clearSprite(key: string){
    if(!this.sprites[key]){ return; }

    this.sceneService.remove(this.sprites[key]);
    this.sprites[key] = null;
  }
  getSpritePosition(key:string): Vector3 | null{
    if(!this.sprites[key]){ return null }

    return this.sprites[key].position.clone()
  }

  //Optimalization
  precomputeBVH(object: THREE.Object3D) {


    object.traverse(child => {
      if (child instanceof THREE.Mesh && child.geometry) {
        // Add the BVH to the geometry
        child.geometry.computeBoundsTree = computeBoundsTree;
        child.geometry.disposeBoundsTree = disposeBoundsTree;
        child.geometry.computeBoundsTree();
      }
    });
  }

}
