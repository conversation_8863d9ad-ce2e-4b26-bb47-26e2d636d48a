import { Injectable } from '@angular/core';
import {Dataset} from "../../models/dataset/dataset.model";
import {HelpersService} from "../helpers/helpers.service";
import {DatasetRow} from "../../models/dataset_row/dataset-row.model";

@Injectable({
  providedIn: 'root'
})
export class DatasetsService {

  public datasets: Dataset[] = [];

  constructor(
      protected helpers: HelpersService
  ) { }

  async init(){
    const { data } = await this.helpers.post('/api/editor/datasets/get', { relations: ['rows'], values: true });
    this.datasets = data.datasets.map((dataset: any) => { return new Dataset(dataset) });
  }

  //Rows
  getRows(dataset_id: number): DatasetRow[] {
    const dataset = this.datasets.find((dataset: Dataset) => dataset.id == dataset_id);
    return dataset?.rows || [];
  }
  getRow(row_id: number): DatasetRow | null{
    const row = this.datasets
        .flatMap((dataset: Dataset) => dataset.rows)
        .find((row: DatasetRow) => row.id == row_id);

    return row || null;
  }

}
