import {Injectable} from '@angular/core';
import {HelpersService} from "../helpers/helpers.service";
import {ModelsService} from "../models/models.service";
import {SceneService} from "../scene/scene.service";
import html2canvas from 'html2canvas';
import {UiService} from "../ui/ui.service";
import {Vector3} from "three";
import {InsideNavigationService} from "../inside_navigation/inside-navigation.service";
import {InsideNavigationEnums} from "../../enums/InsideNavigationEnums";
import {CameraService} from "../camera/camera.service";

@Injectable({
  providedIn: 'root'
})
export class MetadataService {

  private perspective_loaded: boolean = false;

  constructor(
      private helpers: HelpersService,
      private modelsService: ModelsService,
      private sceneService: SceneService,
      private cameraService: CameraService,
      private uiService: UiService,
      private insideNavigationService: InsideNavigationService,
  ) { }

  async store(){
    if(!this.modelsService.model){ return; }

    const metadata = this.uiService.is2D()
      ? await this.get2DMetadata()
      : await this.get3DMetadata();

    await this.helpers.post('/api/editor/metadata/store', {
      guid: this.modelsService.model.guid,
      metadata: metadata,
    });
  }
  async get2DMetadata(){
    const metadata = [];

    metadata.push({name: '2D_ambient_lightning_intensity', value: this.sceneService.lightning.intensity});
    metadata.push({name: '2D_camera_position', value: this.sceneService.camera.position});
    metadata.push({name: '2D_controls_target_position', value: this.sceneService.controls.target});

    //Google maps
    if (this.modelsService.sprites.google_maps) {
      metadata.push({name: '2D_google_maps_position', value: this.modelsService.sprites.google_maps.position});
      metadata.push({name: '2D_google_maps_area', value: this.modelsService.sprites.google_maps.area});
    }

    return metadata;
  }
  async get3DMetadata(){
    const metadata = [];

    metadata.push({name: 'perspective', value: this.uiService.perspective});
    metadata.push({name: 'preview_img', value: await this.getPreviewBlob()});
    metadata.push({name: 'ambient_lightning_intensity', value: this.sceneService.lightning.intensity});

    //Camera & Target
    if(this.uiService.perspective == InsideNavigationEnums.ORBIT){
      metadata.push({name: 'camera_position', value: this.sceneService.camera.position});
      metadata.push({name: 'controls_target_position', value: this.sceneService.controls.target});
    }
    else if(this.uiService.perspective == InsideNavigationEnums.FIRST_PERSON){
      metadata.push({name: 'first_person_camera_position', value: this.sceneService.camera.position});
      metadata.push({name: 'first_person_controls_target_position', value: this.sceneService.controls.target});
    }

    //Google Maps
    if (this.modelsService.sprites.google_maps) {
      metadata.push({name: 'google_maps_position', value: this.modelsService.sprites.google_maps.position});
      metadata.push({name: 'google_maps_area', value: this.modelsService.sprites.google_maps.area});
    }

    return metadata;
  }

  async load(){
    this.uiService.is2D()
      ? await this.load2D()
      : await this.load3D();
  }
  async load2D(){
    //Ambient Lightning
    const ambient_intensity = Number(this.get('2D_ambient_lightning_intensity', this.sceneService.lightning.intensity));
    this.sceneService.changeLightningIntensity(ambient_intensity);

    //Camera
    const camera_position = this.get('2D_camera_position',  JSON.stringify(new Vector3(0, 5, 0)));
    this.sceneService.setCameraPosition(JSON.parse(camera_position))

    //Target position
    const controls_target_position = this.get('2D_controls_target_position',  JSON.stringify(new Vector3(0, 0, 0)));
    this.sceneService.setTargetPosition(JSON.parse(controls_target_position))

    if(this.modelsService.sprites?.google_maps){
      const google_maps_position = this.get('2D_google_maps_position', JSON.stringify(this.modelsService.sprites.google_maps.position));
      this.modelsService.sprites.google_maps.position.copy(JSON.parse(google_maps_position));
      await this.modelsService.updateSateliteArea( Number(this.get('2D_google_maps_area', 240)) );
    }
  }
  async load3D(){
    //Perspective
    if(!this.perspective_loaded){
      //Load only on the initial metadata set
      this.insideNavigationService.selectView( this.get('perspective', InsideNavigationEnums.ORBIT) );
      this.perspective_loaded = true;
    }

    //Ambient Lightning
    const ambient_intensity = Number(this.get('ambient_lightning_intensity', this.sceneService.lightning.intensity));
    this.sceneService.changeLightningIntensity(ambient_intensity);

    //Camera & Target metadata
    let camera_position, controls_target_position;
    if(this.uiService.perspective == InsideNavigationEnums.ORBIT){
      camera_position = this.get('camera_position');
      controls_target_position = this.get('controls_target_position');
    }
    else if(this.uiService.perspective == InsideNavigationEnums.FIRST_PERSON){
      camera_position = this.get('first_person_camera_position');
      controls_target_position = this.get('first_person_controls_target_position');
    }

    //Set Camera
    camera_position = camera_position ? JSON.parse(camera_position) : this.sceneService.getCameraPosition();
    this.uiService.rendering
        ? this.cameraService.setSmoothCameraPosition(camera_position)
        : this.sceneService.setCameraPosition(camera_position)

    //Set Target
    controls_target_position = controls_target_position ? JSON.parse(controls_target_position) : this.sceneService.getTargetPosition();
    this.uiService.rendering
        ? this.cameraService.setSmoothTargetPosition(controls_target_position)
        : this.sceneService.setTargetPosition(controls_target_position)

    //Google Maps
    if(this.modelsService.sprites?.google_maps){
      const google_maps_position = this.get('google_maps_position', JSON.stringify(this.modelsService.sprites.google_maps.position));
      this.modelsService.sprites.google_maps.position.copy(JSON.parse(google_maps_position));
      await this.modelsService.updateSateliteArea( Number(this.get('google_maps_area', 240)) );
    }
  }

  get(key: string, def: any = null){
    if(!this.modelsService.model){ return null; }

    const metadata = this.modelsService.model.metadata.find((data: any) => {
      return data.name == key;
    })

    return metadata?.value || def;
  }
  async getPreviewBlob(){
    const canvas = document.querySelector('canvas');
    if(!canvas){ return null; }

    return (await html2canvas(canvas, {
      scale: 0.5,
    })).toDataURL('image/png')
  }

}
