import { Injectable } from '@angular/core';
import {BaseIndicatorService} from "../BaseIndicator/base-indicator.service";
import {HelpersService} from "../helpers/helpers.service";
import {SceneService} from "../scene/scene.service";
import {ModelsService} from "../models/models.service";
import {HorizontalVectorTriangle} from "../../models/HorizontalVectorTriangle/horizontal-vector-triangle";
import {VerticalVectorTriangle} from "../../models/VerticalVectorTriangle/vertical-vector-triangle";
import {CalculationsService} from "../calculations/calculations.service";
import {UiService} from "../ui/ui.service";
import * as THREE from "three";
import {Vector3} from "three";
import {Vector} from "html2canvas/dist/types/render/vector";
import {VectorTriangle} from "../../models/VectorTriangle/vector-triangle";

@Injectable({
  providedIn: 'root'
})
export class LevelService extends BaseIndicatorService{


  public level_type: 'horizontal' | 'vertical' = 'horizontal';

  //Triangles
  public triangles: VectorTriangle[] = [];

  //Guidelines
  public guidelines: THREE.Mesh[] = [];
  public guidelines_normal: Vector3 | null = null;

  constructor(
    helpers: HelpersService,
    sceneService: SceneService,
    calculationsService: CalculationsService,
    modelsService: ModelsService,
    private uiService: UiService,
  ) {
    super(helpers, sceneService, calculationsService, modelsService);
  }

  toggle(){
    this.toggleIndicator();

    this.clearGuidelines();
    this.setRaycastModels(this.modelsService.getModelObjects( this.uiService.mode == 'FULL' ? 'high' : 'low' ));
  }
  toggleType(type: 'horizontal' | 'vertical'){
    this.level_type = type;
  }

  confirm(event: MouseEvent){
    if(!this.state || event.button !== 0){ return }

    this.placeMarker(event);

    this.level_type == 'vertical'
      ? this.createVerticalGuideline()
      : this.createHorizontalGuidelines(event);

    this.setTriangle();
  }

  //Triangles
  highlightTriangle(index: number){
    const triangle = this.triangles[index];
    triangle.setLineColor('short', this.indicator_remove_color);
    triangle.setPolygonColor(this.indicator_remove_color);
    triangle.setCornerColor('a', this.indicator_remove_color);
  }
  dehighlightTriangle(index: number){
    const triangle = this.triangles[index];
    triangle.setLineColor('short', this.indicator_highlight_color);
    triangle.setPolygonColor(this.indicator_confirmed_color);
    triangle.setCornerColor('a', this.indicator_highlight_color);
  }
  removeTriangle(index: number){
    const triangle = this.triangles[index];
    triangle.clear();

    this.triangles.splice(index, 1);
  }
  setTriangle(){
    if(this.markers.length < 2){ return; }

    const marker = this.markers[0];
    const next_marker = this.markers[1]

    const triangle = this.createTriangle(marker.position, next_marker.position);
    this.triangles.push(triangle);

    this.clearIndicatorPoints();
    this.clearGuidelines();
  }
  createTriangle(pos: Vector3, next_pos: Vector3): VectorTriangle {
    const Instnace = this.level_type == 'horizontal'
      ? HorizontalVectorTriangle
      : VerticalVectorTriangle;

    const triangle = new Instnace(
      this.sceneService,
      this.calculationsService,
      pos.clone(),
      next_pos.clone()
    )

    triangle.appendLine(
      'short',
      this.indicator_highlight_color,
      this.indicator_radius * .5,
    );
    triangle.appendPolygon(this.indicator_confirmed_color);
    triangle.appendCorner('a', this.indicator_highlight_color);

    return triangle;
  }
  clearTriangles(){
    for(const triangle of this.triangles){
      triangle.clear();
    }
    this.triangles = [];
  }

  //Guidelines
  createHorizontalGuidelines(event: MouseEvent){
    for(let index in this.markers){
      if((Number(index) % 2)){ continue }


      const position = this.markers[index].position.clone();
      const directions = this.calculationsService.getAverageFaceNormals(event, this.modelsService.getModelObjects());
      if(!directions){ return; }

      this.guidelines_normal = directions.down.clone();
      const lines  = [
        {opacity: 0, width_factor: 1.25},
        {opacity: .1, width_factor: .5},
      ]

      for(const line_data of lines){
        for(const index of ['left', 'right', 'forward', 'backward'] as const ){
          const dir = directions[index];

          const { x, z } = this.modelsService.getModelSize();
          const line = this.helpers.createLineTube(
            position,
            position.clone().add( dir.clone().multiplyScalar(Math.max(x, z)) ),
            BaseIndicatorService.COLOR_CONFIRM,
            this.indicator_radius * line_data.width_factor
          );
          (line.material as THREE.MeshBasicMaterial).opacity = line_data.opacity
          line.userData['type'] = index;
          line.userData['direction'] = dir;

          this.guidelines.push(line);
          this.sceneService.add(line)
        }
      }
    }
  }
  createVerticalGuideline(){
    this.markers.forEach((marker: THREE.Mesh, index: number) => {
      if(index % 2){ return; }

      const { x, z } = this.indicator.position.clone();
      const { min, max } = this.modelsService.getModelBoundingBox();
      const line = this.helpers.createLineTube(
        new Vector3( x, min.y, z ),
        new Vector3(x, max.y, z),
        BaseIndicatorService.COLOR_CONFIRM,
        this.indicator_radius * .5,
      );

      (line.material as THREE.MeshBasicMaterial).opacity = .1;

      this.guidelines.push(line);
      this.sceneService.add(line)
    });

  }
  clearGuidelines(){
    this.guidelines.forEach((line: THREE.Mesh) => {
      this.sceneService.remove(line)
    })

    this.guidelines = [];
    this.guidelines_normal = null;
  }

}
