import {effect, Injectable} from '@angular/core';
import {SceneService} from "../scene/scene.service";
import {HelpersService} from "../helpers/helpers.service";
import {ModelsService} from "../models/models.service";
import {UiService} from "../ui/ui.service";
import {Location} from "../../models/location/location.model";
import {CalculationsService} from "../calculations/calculations.service";
import {BaseIndicatorService} from "../BaseIndicator/base-indicator.service";
import * as THREE from "three";

@Injectable({
    providedIn: 'root'
})
export class GpsService extends BaseIndicatorService{

    //gps
    public location: Location;

    //Indicator
    public indicator_height: number = 100
    public indicator_opacity: number = 0.66

    constructor(
      helpers: HelpersService,
      sceneService: SceneService,
      calculationsService: CalculationsService,
      modelsService: ModelsService,

      private uiService: UiService,
    ) {
      super(helpers, sceneService, calculationsService, modelsService);
      this.location = new Location();
      this.indicator_radius = .2;

      effect(() => {
        this.markers_signal();
        this.setGPSLocations();
      });
    }

    toggleGPS(){
      this.toggleIndicator({ clear: false });
      this.setLocation();
    }

    //Indicator
    override generateIndicator(): THREE.Mesh {
      let indicator = this.uiService.is2D()
        ? this.helpers.createDot(this.indicator_color, this.indicator_radius)
        : this.helpers.createCylinder(this.indicator_color, this.indicator_radius, this.indicator_height, this.indicator_opacity);

      indicator.visible = false;
      indicator.userData['gps_dms_lat'] = null;
      indicator.userData['gps_dms_lon'] = null;

      return indicator;
    }

    //Location
    locate(event: MouseEvent) {
        if (!this.state) { return; }
        if(this.sceneService.controlling){
            this.indicator.visible = false;
            return;
        }

        this.moveIndicator(event);

        const { dms_lat,  dms_lon } = this.location.calculateLocation(this.indicator.position.clone(), this.modelsService.sprites.google_maps?.position?.clone())
        this.indicator.userData['gps_dms_lat'] = dms_lat;
        this.indicator.userData['gps_dms_lon'] = dms_lon;
    }
    locateNonCanvas(){
        if(!this.state){ return; }

        this.indicator.visible = false;
        this.indicator.userData['gps_dms_lat'] = null;
        this.indicator.userData['gps_dms_lon'] = null;

        this.moveIndicatorNonCanvas();
    }

    //GPS
    setLocation(){
      this.location = new Location(this.modelsService.location.coordinates)
    }
    setGPSLocations(){
      this.markers.forEach((marker: THREE.Mesh) => {

        const { dms_lat,  dms_lon } = this.location.calculateLocation(marker.position.clone(), this.modelsService.sprites.google_maps?.position?.clone())
        marker.userData['gps_dms_lat'] = dms_lat;
        marker.userData['gps_dms_lon'] = dms_lon;
      })

    }

}
