import { Injectable } from '@angular/core';
import {SceneService} from "../scene/scene.service";
import {HelpersService} from "../helpers/helpers.service";

@Injectable({
  providedIn: 'root'
})
export class DebugService {

  target_indicator: any;
  public target_visible: boolean = false;

  constructor(
      protected helpers: HelpersService,
      protected sceneService: SceneService,
  ) {}

  toggleTargetVisibility(){
    this.target_visible = !this.target_visible;

    if(this.target_visible){
      this.target_indicator = this.helpers.createDot('#FF0000', 0.025)
      this.sceneService.add(this.target_indicator);
    }
    else{
      this.sceneService.remove(this.target_indicator);
      this.target_indicator = null;
    }
  }
  updateTargetIndicator(){
    if(!this.target_visible){ return; }

    this.target_indicator.position.copy( this.sceneService.getTargetPosition() );
  }


  update(){
    this.updateTargetIndicator();

  }


}
