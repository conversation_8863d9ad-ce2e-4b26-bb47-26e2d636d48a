import {effect, Injectable} from '@angular/core';
import * as THREE from "three";
import {SceneService} from "../scene/scene.service";
import {UiService} from "../ui/ui.service";
import {ModelsService} from "../models/models.service";
import {HelpersService} from "../helpers/helpers.service";
import {CalculationsService} from "../calculations/calculations.service";
import {BaseIndicatorService} from "../BaseIndicator/base-indicator.service";
import {MeshBasicMaterial, Vector2, Vector3} from "three";
import {CameraService} from "../camera/camera.service";
import {Decimal} from "decimal.js";
import {ParsedDate} from "../../models/ParsedDate/parsed-date";
import {ModelObject} from "../../models/model_object/model-object.model";

@Injectable({
  providedIn: 'root'
})
export class MeasurementsService extends BaseIndicatorService{

  public mode: 'length' | 'volume' = 'length';
  public model_quality = '';

  //Volume
  public volume: number = 0;
  public volume_step: null | 'position' | 'select'| 'compile_mesh' | 'confirm' | 'calculate' | 'results' = null;
  public volume_walls: THREE.Mesh[] = [];
  public volume_polygon_points: Vector3[] = [];
  public volume_mesh: THREE.Mesh | null = null;

  //Volume Blocks
  public volume_instanced_block: THREE.InstancedMesh | null = null;
  public volume_instanced_block_index: number = 0;
  public volume_instanced_block_count_per_axis: number = 150;
  public volume_bounding_box: THREE.Box3 | null = null;
  public volume_instanced_block_dummy = new THREE.Object3D();

  //Volume config
  public volume_precision: number = .2;

  //Volume progress
  public volume_total_steps: number = 0;
  public volume_current_step: number = 0;
  public volume_steps_percentage: number = 0;

  public volume_started_at: number = 0;
  public volume_duration: string = '00:00';
  public volume_complete_eta: string = '00:00';

  //Volume results
  public volume_visualisation: 'model' | 'volume' = 'model';

  // Measurements
  public length_state: boolean = false;
  public lines: any[] = [];
  public distances: number[] = [];
  public total_distance = 0;
  public total_surface = 0;

  constructor(
      helpers: HelpersService,
      sceneService: SceneService,
      calculationsService: CalculationsService,
      modelsService: ModelsService,
      private uiService: UiService,
      private cameraService: CameraService,
  ) {
    super(helpers, sceneService, calculationsService, modelsService);

    this.indicator_options.place_on_markers = true;
    effect(() => {
      this.markers_signal()
      this.calculateMeasure();
      this.createVolumeWalls();
    });
  }

  //State
  toggle(){
    if(this.mode == 'length'){
      this.length_state = !this.length_state;
      if(!this.length_state){
        this.toggleIndicator({
          state: false,
          clear: this.markers.length == 1, //Clear only when 1 disconnected dot is placed
        })
        return
      }

      this.indicator_confirmed_color = MeasurementsService.COLOR_CONFIRM

      this.toggleIndicator({state: true})
      this.setMeasureModelQuality();
    }
    else if(this.mode == 'volume'){
      if(this.volume_step){
        this.clearVolume();
        this.volume_step = null;
        return;
      }

      this.positonCameraForVolume();
      this.indicator_confirmed_color = MeasurementsService.COLOR_SELECT
    }
  }
  clearLength(){
    this.clearIndicatorPoints();
    this.clearMeasureLines();

    this.distances = [];
    this.total_distance = 0;
    this.total_surface = 0;
  }
  clearVolume(){
    if(this.volume_step == 'select'){
      this.toggleIndicator();
    }

    this.volume = 0;
    this.volume_total_steps = 0;
    this.volume_current_step = 0;
    this.volume_steps_percentage = 0;

    this.setVolumeVisualisation('model');
    this.clearVolumeWalls()
    this.clearVolumeBlocks()
    this.clearVolumeMesh();
  }
  clearVolumeWalls(){
    this.volume_polygon_points = [];

    this.volume_walls.forEach((wall: THREE.Mesh) => {
      this.sceneService.remove(wall);
    })
    this.volume_walls = [];
  }
  clearVolumeBlocks(){
    if(!this.volume_instanced_block){ return; }
    this.volume_instanced_block.count = 0;
    this.sceneService.remove( this.volume_instanced_block );
    this.volume_instanced_block = null;
    this.volume_instanced_block_index = 0;
  }
  clearMeasureLines(){
    this.lines.map((line: THREE.Mesh) => { this.sceneService.remove(line); })
    this.lines = [];
  }
  clearVolumeMesh(){
    this.sceneService.remove(this.volume_mesh);
    this.volume_mesh = null;
  }

  //Modes
  setMode(mode: typeof this.mode){
    this.toggle();

    this.toggleIndicator({ state: false });
    this.clearLength();
    this.clearVolume();

    this.mode = mode;
    this.toggle();
  }


  //---Volume---//


  //position
  positonCameraForVolume(){
    this.volume_step = 'position';

    const center =  this.modelsService.getModelCenter();
    const size = this.modelsService.getModelSize();

    const { min, max } = this.modelsService.getModelBoundingBox();
    const {x, z} = center;

    //Long side of the model, should align horizontally with the screen, if the camera is positioned with the longer diagonal vector, i guess?
    const diag_1 = new Vector3().subVectors(
      new Vector3(min.x, 0, min.z),
      new Vector3(max.x, 0, max.z),
    );
    const diag_2 = new Vector3().subVectors(
      new Vector3(min.x, 0, max.z),
      new Vector3(max.x, 0, min.z),
    );

    //Short side direction
    const direction = diag_1.length() > diag_2.length()
      ? diag_1.normalize()
      : diag_2.normalize();

    //Set Position
    this.cameraService.setSmoothCameraPosition(new Vector3(x, (min.y + Math.max(size.x, size.y, size.z) * .75), z).add(direction.multiplyScalar(1)));
    this.sceneService.setTargetPosition(new Vector3(x, min.y, z));
    this.selectVolumeArea();
  }

  //Select
  confirmVolumeSelection(){
    this.volume_bounding_box = this.helpers.getObjectBoundingBox( this.volume_walls );
    this.compileMesh();
  }
  selectVolumeArea(){
    this.volume_step = 'select'
    this.indicator_radius = .25;

    this.clearVolumeWalls();
    this.clearIndicatorPoints();
    this.toggleIndicator({ state: true });
  }
  isVolumeEnclosed(){
    if(this.markers.length < 3){ return false; }

    const first_pos = this.markers[0].position;
    const last_pos = this.markers[this.markers.length - 1].position;

    return !first_pos.distanceTo( last_pos )
  }
  createVolumeWalls(){
    if(this.mode !== 'volume' || this.volume_step !== 'select'){ return; }

    this.clearVolumeWalls();
    if(this.markers.length < 2){ return; }

    const { min, max } = this.modelsService.getModelBoundingBox();

    //Create Buffer
    this.markers.forEach((marker: THREE.Mesh, index: number) => {
      const next_marker = this.markers[index + 1];
      if(!next_marker){ return; }

      const vertices = [
        marker.position.clone().setY(min.y), marker.position.clone().setY(max.y), next_marker.position.clone().setY(max.y),
        marker.position.clone().setY(min.y), next_marker.position.clone().setY(min.y), next_marker.position.clone().setY(max.y),
      ];
      const buffer = vertices.flatMap((vertex: Vector3) => [vertex.x, vertex.y, vertex.z]);
      const wall = this.helpers.createFromBuffer(buffer, MeasurementsService.COLOR_SELECT);
      (wall.material as THREE.Material).depthTest = true;
      wall.userData['wall_type'] = 'barrier';

      this.volume_walls.push(wall);
      this.sceneService.add(wall);
      this.volume_polygon_points.push( marker.position.clone() );
    });

    //Disable indicator selector after enclosing the volume area
    if(this.isVolumeEnclosed()){
      this.toggleIndicator({ state: false, clear: false });
    }
  }
  volumeHelp(){
    this.uiService.showConfirmModal({
      title: 'Volume modelmuren',
      large: true,
      message: this.volumeHelpText,
      confirm_text: 'OK',
      confirm_color: 'success',
    });
  }

  toggleWallType(wall: THREE.Mesh){
    wall.userData['wall_type'] = wall.userData['wall_type'] == 'barrier'
      ? 'model'
      : 'barrier';
  }
  highlightWall(wall: THREE.Mesh){
    console.log('highlight');
    (wall.material as THREE.MeshBasicMaterial).color.set(BaseIndicatorService.COLOR_HIGHLIGHT);
  }
  dehighlightWall(wall: THREE.Mesh){
    const color = wall.userData['wall_type'] == 'barrier'
      ? BaseIndicatorService.COLOR_SELECT
      : BaseIndicatorService.COLOR_INDICATOR;

    (wall.material as THREE.MeshBasicMaterial).color.set(color);
  }

  //Compile
  async compileMesh(){
    if(!this.volume_bounding_box || !this.volume_walls){ return; }

    this.volume_step = 'compile_mesh';
    await this.helpers.sleep(100);

    this.volume_walls.forEach((wall: THREE.Mesh) => {
      if(wall.userData['wall_type'] == 'barrier'){
        wall.visible = false
      }
    })
    this.createVolumeMesh()
    this.clearIndicatorPoints();

    this.volume_step = 'confirm';
  }
  createVolumeMesh(){
    //Firstly create a mesh, based on the bounding box ( vector calculation )
    const vertices: THREE.Vector3[] = [];

    //Find selected vertices
    this.modelsService.getModelObjects('low', { include_maps: false }).forEach((object: THREE.Object3D) => {
      object.traverse((child: THREE.Object3D) => {
        if(!(child instanceof THREE.Mesh) || !this.volume_bounding_box){ return; }

        const geometry = child.geometry as THREE.BufferGeometry;
        const position = geometry.getAttribute('position');
        const index = geometry.getIndex();
        if(!index){ return; }

        for (let i = 0; i < index.count; i += 3) {
          const a = new Vector3().fromBufferAttribute(position, index.getX(i)).applyMatrix4(child.matrixWorld);
          const b = new Vector3().fromBufferAttribute(position, index.getX(i + 1)).applyMatrix4(child.matrixWorld);
          const c = new Vector3().fromBufferAttribute(position, index.getX(i + 2)).applyMatrix4(child.matrixWorld);

          const is_inside_bounding_box = this.volume_bounding_box.containsPoint(a) || this.volume_bounding_box.containsPoint(b) || this.volume_bounding_box.containsPoint(c);
          if(!is_inside_bounding_box){ continue; }

          const face_center = new THREE.Vector3().add(a).add(b).add(c).multiplyScalar( 1/3 );
          const face_center_v2 = new Vector2(face_center.x, face_center.z);

          const is_inside_polygon = this.calculationsService.isPointInsideV2Polygon(face_center_v2, this.v2Polygon)
          if(!is_inside_polygon){ continue; }

          vertices.push(a, b, c);
        }
      });
    })

    //Create, precompute, and append mesh from buffered vertices
    const buffer = vertices.flatMap((vertex: Vector3) => [vertex.x, vertex.y, vertex.z]);
    this.volume_mesh = this.helpers.createFromBuffer(buffer, BaseIndicatorService.COLOR_SELECT);
    (this.volume_mesh.material as THREE.Material).opacity = .3;
    this.volume_mesh.name = 'Volume Selectie';
    this.volume_mesh.renderOrder = 998;

    this.modelsService.precomputeBVH( this.volume_mesh );
    this.sceneService.add(this.volume_mesh);
  }

  //Confirm
  confirmVolumePrecision(){
    this.calculateVolume()
  }

  //Calculate
  async calculateVolume(){
    if(!this.volume_bounding_box || !this.volume_walls || !this.volume_mesh){ return; }



    this.volume_step = 'calculate';
    this.volume_walls.forEach((wall: THREE.Object3D) => wall.visible = false);
    this.volume_mesh.visible = false;

    const { min, max } = this.volume_bounding_box;

    this.volume_total_steps = ((max.x - min.x) / this.volume_precision) * (max.z - min.z) / this.volume_precision
    this.volume_current_step = 0;
    this.volume_started_at = new ParsedDate().timestamp
    this.volume = 0;

    for(let x = min.x; x <= max.x; x += this.volume_precision){
      for(let z = min.z; z <= max.z; z += this.volume_precision){

        if(this.volume_step !== 'calculate'){
          return;
        }

        this.volume_current_step++;
        this.volume_steps_percentage = Math.min(Math.max(Number((this.volume_current_step / this.volume_total_steps * 100).toFixed(1)), this.volume_steps_percentage), 100);

        const v2_point = new Vector2(x, z);
        if(!this.calculationsService.isPointInsideV2Polygon(v2_point, this.v2Polygon)){
          continue;
        }

        for(let y = min.y; y <= max.y; y += this.volume_precision){
          const v3_point = new Vector3(x, y, z);
          const { inside } = this.calculationsService.isPointInsideModel(v3_point, {
            threshold: .95,
            barrier_objects: this.volume_walls,
            scan_objects: [ this.volume_mesh ],
          });

          if(inside){
            this.volume += (this.volume_precision * this.volume_precision * this.volume_precision)
            this.visualiseVolume(new Vector3(x, y, z));
          }
        }

        if(this.volume_current_step % 5 === 0){
          await this.helpers.sleep(2);
          this.calculateVolumeCompleteEta();
        }
      }
    }


    this.volume = new Decimal(this.volume).toDecimalPlaces(2).toNumber();
    this.volume_step = 'results';
  }
  canVisualiseVolume(position: Vector3): boolean {
    if(!this.volume_bounding_box){ return false; }

    const size = new Vector3();
    this.volume_bounding_box.getSize(size);

    const maxDimension = Math.max(size.x, size.y, size.z);
    const spacing = Number((maxDimension /
      (this.volume_bounding_box.getSize(new Vector3()).length() < 1 ?
        this.volume_instanced_block_count_per_axis / 4 :
        this.volume_instanced_block_count_per_axis)
    ).toFixed(10));

    const precision = this.volume_precision;
    const dot_step = Math.max(1, Math.round(spacing / precision));

    const x_step = Math.round(position.x / precision);
    const y_step = Math.round(position.y / precision);
    const z_step = Math.round(position.z / precision);

    return (x_step % dot_step === 0 && y_step % dot_step === 0 && z_step % dot_step === 0);
  }
  visualiseVolume(position: Vector3): void{
    if(!this.volume_bounding_box){ return; }
    if(!this.canVisualiseVolume(position)){ return; }

    if(!this.volume_instanced_block){
      this.createInstancedBlock();
    }
    if(!this.volume_instanced_block){ return; }

    if(this.volume_instanced_block_index > this.volume_instanced_block.count){
      const old_mesh = this.volume_instanced_block;
      const old_count = this.volume_instanced_block_index;

      this.volume_instanced_block = this.helpers.createInstancedBlock('#00FF00', this.blockSize, this.volume_instanced_block_index + 250);
      (this.volume_instanced_block.material as THREE.Material).opacity = .15;

      const tempMatrix = new THREE.Matrix4();
      for (let i = 0; i < old_count; i++) {
        old_mesh.getMatrixAt(i, tempMatrix);
        this.volume_instanced_block.setMatrixAt(i, tempMatrix);
      }
      this.volume_instanced_block.instanceMatrix.needsUpdate = true;

      // this.clearVolumeBlocks();
      this.sceneService.remove(old_mesh);
      this.sceneService.add(this.volume_instanced_block);
    }

    this.volume_instanced_block_dummy.position.copy(position);
    this.volume_instanced_block_dummy.updateMatrix();

    this.volume_instanced_block.setMatrixAt(this.volume_instanced_block_index, this.volume_instanced_block_dummy.matrix);
    this.volume_instanced_block_index++;

    if (this.volume_instanced_block_index % 50 === 0) {
      this.volume_instanced_block.instanceMatrix.needsUpdate = true;
    }
  }
  createInstancedBlock(){
    this.volume_instanced_block = this.helpers.createInstancedBlock('#00FF00', this.blockSize, 250);
    (this.volume_instanced_block.material as THREE.Material).opacity = 0;
    this.sceneService.add(this.volume_instanced_block);
  }
  calculateVolumeCompleteEta(){
    const time = (new Date()).getTime();
    const passed = time - this.volume_started_at;
    const ms_per_pixel = passed / this.volume_current_step;

    this.volume_duration = this.helpers.msToTime(passed);
    this.volume_complete_eta = this.helpers.msToTime(ms_per_pixel * this.volume_total_steps);
  }

  //Results
  setVolumeVisualisation(type: 'model' | 'volume'){
    this.volume_visualisation = type;

    this.modelsService.model_objects.forEach((model_object: ModelObject) => model_object.object.visible = (type == 'model'));
    this.modelsService.objects.forEach((object: THREE.Object3D) => object.visible = (type == 'model'));
  }

  //utility
  get v2Polygon(): Vector2[] {
    if(!this.volume_polygon_points){ return [] }
    return this.volume_polygon_points.map((point: Vector3) => new Vector2(point.x, point.z));
  }
  get blockSize(): number{
    if(!this.volume_bounding_box){ return 0; }

    const size = new Vector3();
    this.volume_bounding_box.getSize(size);
    return Number((Math.max(size.x, size.y, size.z) / (this.volume_instanced_block_count_per_axis * 3)).toFixed(2));
  }
  get volumeHelpText(): string{
    return `
    <p>Bij het berekenen van het volume binnen een selectie kijkt het systeem of een punt zich in een gesloten ruimte bevindt. Dit gebeurt via een techniek waarbij onzichtbare lijnen worden gestuurd in verschillende richtingen om te controleren of het punt ‘omringd’ is door geometrie.</p>
    <p>Maar soms:</p>
    <ul>
      <li>Wil je maar een deel van een gebouw selecteren (bijvoorbeeld één verdieping of alleen de voorgevel).</li>
      <li>Of is je selectie aan één kant open (zoals bij een halve uitsnede van een gebouw of opengewerkte modellen).</li>
    </ul>
    <p>In zulke gevallen kan het lijken alsof een punt niet binnen het model valt, omdat een van die onzichtbare lijnen geen ‘botsing’ detecteert aan de open kant.</p>
    <p>Door een muur of vlak te markeren als modelmuur, geef je aan dat deze als onderdeel van het gebouw moet worden beschouwd bij de volumeberekening.</p>
    `
  }


  //---Length---//


  //Measure
  calculateMeasure(){
    if(this.mode !== 'length'){ return; }

    this.distances = [];
    this.total_distance = 0;
    this.total_surface = this.calculationsService.calculateSurfaceArea(this.markers.map((dot: any) => dot.position));
    this.total_distance = this.calculationsService.calculateCircumference(this.markers.map((dot: any) => dot.position));
    this.clearMeasureLines();

    if(this.markers.length < 2){ return; }

    for(let i = 0; i < this.markers.length - 1; i++){
      const dot = this.markers[i];
      const next_dot = this.markers[i + 1];

      const distance = dot.position.distanceTo(next_dot.position)
      this.distances.push(Number(distance.toFixed(3)));

      const line = this.helpers.createLine(dot.position, next_dot.position, this.indicator_confirmed_color, this.indicator_radius * 0.5);
      this.lines.push(line);
      this.sceneService.add(line);
    }
  }
  removeMeasure(index: number){
    const line = this.lines[index];
    this.sceneService.remove(line)
    this.lines.splice(index, 1)

    this.removeMarkerByIndex(index);
  }
  setMeasureIndicatorRadius(){
    this.setIndicatorRadius();
    this.calculateMeasure();
  }
  setMeasureModelQuality(quality?: string){
    if(quality){
      this.model_quality = quality
    }
    else{
      this.model_quality = this.uiService.is2D() ? '2D_low' : 'low';
    }

    this.setRaycastModels(this.modelsService.getModelObjects(this.model_quality))
  }

  //Measure Dot
  highlightMeasure(index: number){
    const line = this.lines[index];
    const dot = this.markers[index + 1];

    (dot.material as MeshBasicMaterial).color.set(this.indicator_remove_color);
    line.material.color.set(this.indicator_remove_color);
  }
  dehighlightMeasure(index: number){
    const line = this.lines[index];
    const dot = this.markers[index + 1];

    (dot.material as MeshBasicMaterial).color.set(this.indicator_confirmed_color);
    line.material.color.set(this.indicator_confirmed_color);
  }

}
