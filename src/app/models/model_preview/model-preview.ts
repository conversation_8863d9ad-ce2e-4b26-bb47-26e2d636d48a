import {Location} from "../location/location.model";

export class ModelPreview {

    public id: number;
    public src: string;
    public exif: any;

    public location: Location | undefined;

    constructor(instance: any) {
        this.id = instance.id;
        this.src = instance.src;
        this.exif = instance.exif;

        if(this.exif.lat && this.exif.lon){
            this.location = new Location(`${this.exif.lat}, ${this.exif.lon}`)
        }

    }

    thumbnail(){
        return `https://portal.dronemissions.eu/file-thumbnail/${this.src}`
    }
    url(){
        return `https://portal.dronemissions.eu/file/${this.src}`
    }

    fullscreen(){
        let width, height;

        const max_y = window.innerHeight;
        const max_x = window.innerWidth;

        width = max_x * .95;
        height = this.exif.height * ( width / this.exif.height );

        if(height > max_y * .95){
            height = max_y * .95;
            width = this.exif.width * ( height / this.exif.height );
        }

        return { height, width }
    }

}
