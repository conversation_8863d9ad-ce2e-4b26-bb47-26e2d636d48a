import {Dataset} from "../dataset/dataset.model";
import {DatasetRow} from "../dataset_row/dataset-row.model";

export class ModelSelection {

    public name: string;
    public color: string;
    public type: string;
    public guid: string;
    public size: number;
    public object: any;

    public dataset_row_id: number;

    public surface_area: number;
    public circumference: number;

    public dataset: Dataset | null;
    public dataset_row: DatasetRow | null;

    public depth_test: boolean = true;

    constructor(selection: any) {
        this.name = selection.name;
        this.color = selection.color;
        this.type = selection.type;
        this.guid = selection.guid;
        this.size = selection.size;
        this.dataset_row_id = selection.dataset_row_id;

        this.surface_area = Number(selection.surface_area);
        this.circumference = Number(selection.circumference);

        this.dataset = selection.dataset
            ? new Dataset(selection.dataset)
            : null;
        this.dataset_row = selection.dataset_row
            ? new DatasetRow(selection.dataset_row)
            : null;
    }

}
