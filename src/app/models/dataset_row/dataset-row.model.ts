import {DatasetValue} from "../dataset_value/dataset-value";

export class DatasetRow {

    public id: number;
    public name: string;
    public values: DatasetValue[];

    constructor(row: any) {
        this.id = row.id;
        this.name = row.name;
        this.values = row.values
            ? row.values.map((value: any) => { return new DatasetValue(value) })
            : [];
    }

    getValue(header: string): any{
        const value = this.values.find((value: DatasetValue) => { return value.header == header; });
        return value ? value?.value : null;
    }

}
