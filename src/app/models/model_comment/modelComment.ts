import {Vector3} from "three";
import {User} from "../user/user.model";
import {ParsedDate} from "../ParsedDate/parsed-date";

export class ModelComment {

  public guid: number;
  public comment: string;
  public camera: Vector3;
  public position: Vector3;
  public user: User;
  public date: ParsedDate;

  constructor(comment: any) {
    this.guid = comment.guid;
    this.comment = comment.comment;
    this.user = new User(comment.user);
    this.date = new ParsedDate(comment.created_at)

    const camera = JSON.parse(comment.camera);
    this.camera = new Vector3(camera.x, camera.y, camera.z);

    const position = JSON.parse(comment.position)
    this.position = new Vector3(position.x, position.y, position.z);
  }

}
