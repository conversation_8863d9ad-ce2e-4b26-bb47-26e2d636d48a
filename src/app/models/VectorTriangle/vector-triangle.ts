import {Mesh, Vector3} from "three";
import {SceneService} from "../../services/scene/scene.service";
import {HelpersService} from "../../services/helpers/helpers.service";
import * as THREE from 'three';
import {CalculationsService} from "../../services/calculations/calculations.service";

export abstract class VectorTriangle {

  helpers: HelpersService;
  scene: SceneService;
  calculations: CalculationsService;

  abstract point_a: Vector3;
  abstract point_b: Vector3;
  abstract point_c: Vector3;

  hypotenuse_side: Mesh | null = null;
  short_side: Mesh | null = null;
  long_side: Mesh | null = null;

  corner_a: Mesh | null = null;
  corner_b: Mesh | null = null;
  corner_c: Mesh | null = null;

  polygon: Mesh | null = null;

  constructor(scene: SceneService, calculations: CalculationsService) {
    this.helpers = new HelpersService();
    this.scene = scene;
    this.calculations = calculations;
  }

  clear(){
    this.removeCorner();
    this.removeLines()
    this.removePolygon();
  }

  //Polygon
  appendPolygon(color: string){
    this.polygon = this.helpers.createPolygon([this.point_a, this.point_b, this.point_c], color);
    this.scene.add(this.polygon);
  }
  removePolygon(){
    this.scene.remove(this.polygon);
  }
  setPolygonColor(color: string) {
    if (!this.polygon) { return }

    const mat = this.polygon.material as THREE.MeshBasicMaterial;
    mat.color.set(color);
  }

  //Points
  getPoint(dot: 'a' | 'b' | 'c'): Vector3{
    switch (dot){
      case "a": return this.point_a;
      case "b": return this.point_b;
      case "c": return this.point_c;
    }
  }

  //corners
  appendCorner(corner_index: 'a' | 'b' | 'c', color: string): void{

    if(corner_index == 'a'){
      const new_point_b = this.calculations.interpolatePosition(this.point_a, this.point_b, .75);
      const new_point_c = this.calculations.interpolatePosition(this.point_a, this.point_c, .75);

      this.corner_a = this.helpers.createPolygon([
        this.point_a,
        new_point_b,
        new_point_c,
      ], color, { opacity: .8 });
      this.scene.add(this.corner_a);
    }
    else if(corner_index == 'b'){
      const new_point_a = this.calculations.interpolatePosition(this.point_b, this.point_a, .75);
      const new_point_c = this.calculations.interpolatePosition(this.point_b, this.point_c, .75);

      this.corner_b = this.helpers.createPolygon([
        new_point_a,
        this.point_b,
        new_point_c,
      ], color, { opacity: .8 });
      this.scene.add(this.corner_b);
    }
    else if(corner_index == 'c'){
      const new_point_a = this.calculations.interpolatePosition(this.point_c, this.point_a, .75);
      const new_point_b = this.calculations.interpolatePosition(this.point_c, this.point_b, .75);

      this.corner_c = this.helpers.createPolygon([
        new_point_a,
        new_point_b,
        this.point_c,
      ], color, { opacity: .8 });
      this.scene.add(this.corner_c);
    }
  }
  removeCorner(){
    this.scene.remove(this.corner_a);
    this.scene.remove(this.corner_b);
    this.scene.remove(this.corner_c);
  }
  getCorner(corner_index: 'a' | 'b' | 'c'): Mesh | null{
    switch (corner_index){
      case "a": return this.corner_a;
      case "b": return this.corner_b;
      case "c": return this.corner_c;
    }
  }
  setCornerColor(corner_index: 'a' | 'b' | 'c', color: string){
    const mesh = this.getCorner(corner_index);
    if(!mesh){ return }

    const material = mesh.material as THREE.MeshBasicMaterial
    material.color.set(color);
  }

  //Lines
  appendLines(color: string, thickness: number){
    this.hypotenuse_side = this.helpers.createLine(this.point_a, this.point_c, color, thickness);
    this.long_side = this.helpers.createLine(this.point_a, this.point_b, color, thickness);
    this.short_side = this.helpers.createLine(this.point_b, this.point_c, color, thickness);

    this.scene.add(this.hypotenuse_side);
    this.scene.add(this.short_side);
    this.scene.add(this.long_side);
  }
  appendLine(line: 'short' | 'long' | 'hypotenuse', color: string, thickness: number){
    let mesh;

    if(line == 'hypotenuse'){
      this.hypotenuse_side = this.helpers.createLine(this.point_a, this.point_c, color, thickness);
      this.scene.add(this.hypotenuse_side);
    }
    else if(line == 'long'){
      this.long_side = this.helpers.createLine(this.point_a, this.point_b, color, thickness);
      this.scene.add(this.long_side);
    }
    else if (line == 'short'){
      this.short_side = this.helpers.createLine(this.point_b, this.point_c, color, thickness);
      this.scene.add(this.short_side);
    }
  }
  removeLines(){
    this.scene.remove(this.hypotenuse_side);
    this.scene.remove(this.short_side);
    this.scene.remove(this.long_side);
  }
  setLinesColor(color: string){
    // @ts-ignore
    this.short_side?.material.color.set(color);
    // @ts-ignore
    this.long_side?.material.color.set(color);
    // @ts-ignore
    this.hypotenuse_side?.material.color.set(color);
  }
  setLineColor(line: 'short' | 'long' | 'hypotenuse', color: string){
    const mesh = this.getLine(line);
    if(!mesh){ return }

    const material = mesh.material as THREE.MeshBasicMaterial
    material.color.set(color);
  }
  getLine(line: 'short' | 'long' | 'hypotenuse'){
    switch (line){
      case "short": return this.short_side;
      case "long": return this.long_side;
      case "hypotenuse": return this.hypotenuse_side;
    }
  }

  getDistances(){
    const distances: any = {
      short:this.point_b.distanceTo(this.point_c),
      long: this.point_a.distanceTo(this.point_b),
      hypotenuse: this.point_a.distanceTo(this.point_c),
    }

    for(const i in distances){
      distances[i] = Number(distances[i].toFixed(3));
    }

    return distances;
  }
  getAngles(){
    const a = this.point_b.distanceTo(this.point_c);
    const b = this.point_a.distanceTo(this.point_c);
    const c = this.point_a.distanceTo(this.point_b);

    const angle_a = this.calculateAngle(b, c, a);
    const angle_b = this.calculateAngle(a, c, b);
    const angle_c = this.calculateAngle(a, b, c);

    return {
      a: Number(angle_a.toFixed(2)),
      b: Number(angle_b.toFixed(2)),
      c: Number(angle_c.toFixed(2)),
    };
  }

  private calculateAngle(side1: number, side2: number, opposite: number): number {
    const cosValue = (side1 ** 2 + side2 ** 2 - opposite ** 2) / (2 * side1 * side2);
    const angleRad = Math.acos(Math.max(-1, Math.min(1, cosValue)));
    return THREE.MathUtils.radToDeg(angleRad);
  }

}
