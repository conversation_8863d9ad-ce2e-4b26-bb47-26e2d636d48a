import {computeDestinationPoint, getPreciseDistance, getRhumbLineBearing} from 'geolib';
import {Vector3} from "three";

export class Location {

    public active: boolean;

    public coordinates: string = '';
    public lat: number = 0;
    public lon: number = 0;

    constructor(coordinates: string | null = null) {
        this.active = !!coordinates;
        if(!coordinates){ return; }

        const coordinates_parts = coordinates.split(',');

        this.coordinates = coordinates;
        this.lat = Number(coordinates_parts[0]);
        this.lon = Number(coordinates_parts[1]);
    }

    calculateLocation(position: Vector3, offset?: Vector3 | null): { lat: number, lon: number, dms_lat: string, dms_lon: string }{
        if(offset){
            position.sub(offset);
            position.setY(0)
        }


        const current_location = { latitude: this.lat, longitude: this.lon };
        const { x, z } = position;

        const distance = Math.sqrt(x * x + z * z);
        let bearing = Math.atan2(x, z) * (180 / Math.PI);
        bearing = 360 - ( bearing + 180 );
        const destination = computeDestinationPoint(current_location, distance, bearing);

        const lat = Number(destination.latitude);
        const lon = Number(destination.longitude);
        const { dms_lat, dms_lon } = this.convertToDMS(lat, lon);

        return { lat, lon, dms_lat, dms_lon };
    }
    calculatePosition(location: Location, offset?: Vector3 | null){
        const distance = getPreciseDistance(
            { lat: this.lat, lon: this.lon },
            { lat: location.lat, lon: location.lon },
        );
        const bearing = getRhumbLineBearing(
            { lat: this.lat, lon: this.lon },
            { lat: location.lat, lon: location.lon },
        );

        const angle = (180 - bearing + 360) % 360;
        const radians = angle * (Math.PI / 180);
        const position = new Vector3(
            Math.sin(radians) * distance,
            0,
            Math.cos(radians) * distance
        );

        if(offset){ position.add(offset) }

        return position
    }

    private convertToDMS(lat: number, lon: number): { dms_lat: string, dms_lon: string } {
      const toDMS = (deg: number, isLat: boolean): string => {
        const absolute = Math.abs(deg);
        const degrees = Math.floor(absolute);
        const minutesFloat = (absolute - degrees) * 60;
        const minutes = Math.floor(minutesFloat);
        const seconds = ((minutesFloat - minutes) * 60).toFixed(1);

        const direction = deg >= 0
          ? (isLat ? 'N' : 'E')
          : (isLat ? 'S' : 'W');

        return `${degrees}°${minutes}'${seconds}"${direction}`;
      };

      return {
        dms_lat: toDMS(lat, true),
        dms_lon: toDMS(lon, false)
      };
    }

    distanceToLocation(location: Location){
        return getPreciseDistance(
            { lat: this.lat, lon: this.lon },
            { lat: location.lat, lon: location.lon },
        );
    }


}
