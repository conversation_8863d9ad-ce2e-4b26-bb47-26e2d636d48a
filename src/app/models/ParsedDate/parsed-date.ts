export class ParsedDate {

  private date: Date;
  private static MONTHS: {full: string, short: string}[] = [
    { full: 'januari', short: 'jan' },
    { full: 'februari', short: 'feb' },
    { full: 'maart', short: 'mrt' },
    { full: 'april', short: 'apr' },
    { full: 'mei', short: 'mei' },
    { full: 'juni', short: 'jun' },
    { full: 'juli', short: 'jul' },
    { full: 'augustus', short: 'aug' },
    { full: 'september', short: 'sep' },
    { full: 'oktober', short: 'okt' },
    { full: 'november', short: 'nov' },
    { full: 'december', short: 'dec' }
  ];

  constructor(date_string?: string | Date) {
    this.date = date_string
      ? new Date(date_string)
      : new Date();
  }

  get timestamp(): number{
    return this.date.getTime();
  }
  get day(): number {
    return this.date.getDate();
  }
  get month(): number {
    return this.date.getMonth() + 1;
  }
  get monthName(): string {
    return ParsedDate.MONTHS[this.date.getMonth()].full;
  }
  get monthShort(): string {
    return ParsedDate.MONTHS[this.date.getMonth()].short;
  }
  get year(): number {
    return this.date.getFullYear();
  }

  get formattedDMY(): string {
    return `${this.day.toString().padStart(2, '0')}-${this.month.toString().padStart(2, '0')}-${this.year}`;
  }
  get formattedISO(): string {
    return `${this.year}-${this.month.toString().padStart(2, '0')}-${this.day.toString().padStart(2, '0')}`;
  }

}
