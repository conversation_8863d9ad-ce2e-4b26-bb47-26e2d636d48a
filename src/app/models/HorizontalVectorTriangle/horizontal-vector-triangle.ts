import {Vector3} from "three";
import {VectorTriangle} from "../VectorTriangle/vector-triangle";
import {SceneService} from "../../services/scene/scene.service";
import {CalculationsService} from "../../services/calculations/calculations.service";

export class HorizontalVectorTriangle extends VectorTriangle {

  //A\
  //|  \
  //|    \
  //|      \
  //|        \
  //B---------C
  //Used for caluclating point b

  point_a: Vector3;
  point_b: Vector3;
  point_c: Vector3;

  constructor(scene: SceneService, calculations: CalculationsService,  a: Vector3, c: Vector3 ) {
    super(scene, calculations)

    if(a.y < c.y){
      let temp = c.clone();

      c = a.clone();
      a = temp.clone();
    }

    this.point_a = a;
    this.point_c = c;

    this.point_b = c.clone()
    this.point_b.y = a.clone().y;
  }

}
