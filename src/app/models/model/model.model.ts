import {ModelObject} from "../model_object/model-object.model";
import {ModelSelection} from "../model_selection/model-selection";
import {ModelComment} from "../model_comment/modelComment";

export class Model {

    id: number;
    guid: string;
    name: string;
    coordinates: string;
    address_id: number;
    customer_id: number;
    created_at: string;

    metadata: any[] = []
    objects: ModelObject[] = []
    selections: ModelSelection[] = []
    comments: ModelComment[] = []

    inside: boolean;

    _2D: boolean;
    _3D: boolean;

    _full: boolean;
    _lite: boolean;
    _mobile: boolean;

    _2D_full: boolean;
    _2D_lite: boolean;
    _2D_mobile: boolean;

    _thermal: boolean;

    constructor(
        model: any,
    ) {
        this.id = model.id;
        this.guid = model.guid;
        this.name = model.name;
        this.coordinates = model.coordinates;
        this.address_id = model.address_id;
        this.customer_id = model.customer_id;
        this.metadata = model.metadata;
        this.created_at = model.created_at;

        this.objects = model.objects
            ? model.objects.map((object: any) => new ModelObject(object))
            : [];

        this.selections = model.selections
            ? model.selections.map((object: any) => new ModelSelection(object))
            : [];

        this.comments = model.comments
          ? model.comments.map((object: any) => new ModelComment(object))
          : [];

        this.inside = !!Number(model.inside);

        this._2D = !!Number(model._2D);
        this._3D = !!Number(model._3D);

        this._full = !!this.objects.find((object: ModelObject) => object.quality == 'high') && this._3D;
        this._lite = !!this.objects.find((object: ModelObject) => object.quality == 'low') && this._3D;
        this._mobile = !!this.objects.find((object: ModelObject) => object.quality == 'mobile') && this._3D;

        this._2D_full = !!this.objects.find((object: ModelObject) => object.quality == '2D_high') && this._2D;
        this._2D_lite = !!this.objects.find((object: ModelObject) => object.quality == '2D_low') && this._2D;
        this._2D_mobile = !!this.objects.find((object: ModelObject) => object.quality == '2D_mobile') && this._2D;

        this._thermal = !!this.objects.find((object: ModelObject) => object.quality == 'thermal') && !!Number(model._thermal);
    }

    //Metadata
    getMetaValue(key: string){
        const metadata = this.metadata.find((metadata: any) => metadata.name == key);
        return metadata?.value || null;
    }

}
