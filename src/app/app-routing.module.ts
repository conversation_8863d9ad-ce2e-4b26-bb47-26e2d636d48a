import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {EditorComponent} from "./editor/editor.component";
import {LoginComponent} from "./login/login.component";
import {DisplayComponent} from "./display/display.component";

const routes: Routes = [
  {path: 'display/:model', component: DisplayComponent},
  {path: 'editor/:guid', component: EditorComponent},
  {path: 'login/:token/:path', component: LoginComponent},
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
