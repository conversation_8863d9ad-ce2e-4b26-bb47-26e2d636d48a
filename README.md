# Services Documentatie

## Authentication Service
**Bestand:** `src/app/services/auth/auth.service.ts`  
**Beschrijving:**  
Beheert alle authenticatie-gerelateerde functionaliteit in de applicatie. Regelt gebruikerssessies, verifieert tokens met de backend, en controleert toegang tot beveiligde functies. Houdt de huidige authenticatiestatus en gebruikersrechten bij gedurende de levenscyclus van de applicatie.

### Eigenschappen
- `token: string|null` - Huidige authenticatietoken
- `authenticated: boolean` - Authenticatiestatus
- `user: User|undefined|null` - Huidig gebruikersobject
- `permissions: string[]` - A<PERSON>y met gebruikersrechten

### Methoden
- `verifyAuth()` - Verifieert authenticatietoken met backend
- `login(token: string)` - Stelt authenticatietoken in en verifieert gebruiker
- `hasPermission(permission: string)` - Controleert of gebruiker specifieke rechten heeft

---

## Camera Service
**Bestand:** `src/app/services/camera/camera.service.ts`  
**Beschrijving:**  
Kernservice voor het beheren van cameragedrag in de 3D-omgeving. Implementeert vloeiende camera-overgangen en zoomfunctionaliteit.

### Methoden
- `setSmoothTargetPosition(position: Vector3|{x,y,z})`
- `zoomToObject(event: WheelEvent)`
- `zoomToObjectInOrbit(event: WheelEvent)`
- `zoomToObjectInFirstPerson(event: WheelEvent)`

---

## Datasets Service
**Bestand:** `src/app/services/datasets/datasets.service.ts`  
**Beschrijving:**  
Beheert dataset-functionaliteit voor het koppelen van data aan 3D-modellen.

### Eigenschappen
- `datasets: Dataset[]`

### Methoden
- `init()`
- `getRows(dataset_id: number)`
- `getRow(row_id: number)`

---

## Debug Service
**Bestand:** `src/app/services/debug/debug.service.ts`  
**Beschrijving:**  
Ontwikkelingsservice met visuele debugging-tools voor 3D-omgeving.

### Eigenschappen
- `target_indicator: THREE.Object3D`
- `target_visible: boolean`

### Methoden
- `toggleTargetVisibility()`
- `updateTargetIndicator()`
- `update()`

---

## Google Service
**Bestand:** `src/app/services/google/google.service.ts`  
**Beschrijving:**  
Integreert Google Maps voor satellietbeelden en geografische context.

### Eigenschappen
- `API_KEY: string`

### Methoden
- `sateliteImage(coordinates: string, options: any)`
- `areaToZoom(area: number)`

---

## Helpers Service
**Bestand:** `src/app/services/helpers/helpers.service.ts`  
**Beschrijving:**  
Biedt utility-methoden zoals cookiebeheer, HTTP-verzoeken en meldingen.

### Methoden
- `cookieSet(name: string, value: string, days: number)`
- `post(url: string, data: any)`
- `message(text: string, type: string)`

---

## Measurements Service
**Bestand:** `src/app/services/measurements/measurements.service.ts`  
**Beschrijving:**  
Voor meten van afstanden, oppervlakten en volumes in 3D-modellen.

### Methoden
- `measure(event: MouseEvent)`
- `getRaycastIntersectionPoint(event: MouseEvent, objects: THREE.Object3D[])`

---

## Metadata Service
**Bestand:** `src/app/services/metadata/metadata.service.ts`  
**Beschrijving:**  
Beheert metadata zoals cameraposities en verlichtingsinstellingen.

### Methoden
- `store()`
- `get2DMetadata()`
- `get3DMetadata()`
- `load()`
- `get(key: string, def: any)`
- `getPreviewBlob()`

---

## Models Service
**Bestand:** `src/app/services/models/models.service.ts`  
**Beschrijving:**  
Beheert 3D-modellen en coördineert laad- en toestandsbeheer.

### Eigenschappen
- `model`
- `address_models`
- `location`

### Methoden
- `setModel(guid: string)`
- `openModel(guid: string)`
- `download(options: {url: string, size: number, id: string})`

---

## Preview Service
**Bestand:** `src/app/services/preview/preview.service.ts`  
**Beschrijving:**  
Beheert previews en interactie met previewmarkers.

### Eigenschappen
- `images: ModelPreview[]`
- `closest_image: ModelPreview|null`
- `markers_state: boolean`
- `markers: THREE.Mesh[]`

### Methoden
- `load()`
- `setImages()`
- `setClosestImage()`

---

## Scene Service
**Bestand:** `src/app/services/scene/scene.service.ts`  
**Beschrijving:**  
Beheert de THREE.js scene en configuratie voor rendering en controls.

### Eigenschappen
- `scene: THREE.Scene`
- `renderer: THREE.WebGLRenderer`
- `camera: THREE.Camera`
- `controls: OrbitControls`
- `transformControls: TransformControls`
- `lightning`

### Methoden
- `init(canvasRef: HTMLCanvasElement)`
- `initScene()`
- `initLight()`
- `initControls()`
- `initTransformer()`
- `initGrid()`

---

## Tutorial Service
**Bestand:** `src/app/services/tutorial/tutorial.service.ts`  
**Beschrijving:**  
Beheert interactieve tutorials voor nieuwe gebruikers.

### Eigenschappen
- `state: boolean`
- `page: number`
- `pages: number`

### Methoden
- `close()`
- `open()`
- `previousPage()`
- `nextPage()`

---

## UI Service
**Bestand:** `src/app/services/ui/ui.service.ts`  
**Beschrijving:**  
Beheert UI-status, interacties, modalen, laadtoestanden en meldingen.

### Eigenschappen
- `messages`
- `css`
- `guid`
- `mode`
- `perspective`
- `rendering`
- `loading`
- `render_distance`
- `toggles`
- `modals`

### Methoden
- `is2D()`
- `setMessage(id: string, message: string)`
- `setDownloadMessage(id: string, fetched: number, size: number)`
- `setCss(key: string, value: string|number)`
- `setRenderBasis(distance: number)`  
