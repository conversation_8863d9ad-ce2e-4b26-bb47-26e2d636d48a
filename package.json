{"name": "editor", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.1.0", "@angular/common": "^18.1.0", "@angular/compiler": "^18.1.0", "@angular/core": "^18.1.0", "@angular/forms": "^18.1.0", "@angular/platform-browser": "^18.1.0", "@angular/platform-browser-dynamic": "^18.1.0", "@angular/router": "^18.1.0", "@types/earcut": "^2.1.4", "axios": "^1.7.2", "chart.js": "^4.4.9", "decimal.js": "^10.5.0", "earcut": "^3.0.0", "exif-reader": "^2.0.2", "geolib": "^3.3.4", "html2canvas": "^1.4.1", "meshoptimizer": "^0.21.0", "ngx-color-picker": "^17.0.0", "rxjs": "~7.8.0", "three": "^0.166.1", "three-mesh-bvh": "^0.8.3", "three.meshline": "^1.4.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^18.1.0", "@angular/cli": "^18.1.0", "@angular/compiler-cli": "^18.1.0", "@types/jasmine": "~5.1.0", "@types/node": "^20.17.6", "@types/three": "^0.166.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.2"}}