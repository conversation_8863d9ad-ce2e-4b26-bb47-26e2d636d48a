body{
  overflow: hidden;
}
a{
  color: inherit;
  text-decoration: none;
}

.render-canvas {
    width: 100%;
    height: 100%;
    display: block;
}
input{
    outline: none;
}

/*Tutorial Modal*/
.tutorial-modal{
    background-color: rgba(0, 0, 0, 0.66);
}
.tutorial-modal .card-body{
    overflow: auto;
    height: 350px;
}
.tutorial-modal .card{
    width: 1000px!important;
    max-width: 95%;
}
.tutorial-modal img{
    border-radius: .5rem;
}
.tutorial-modal .gif{
    height: 260px
}

/*Modals*/
.modal-dialog{
  margin-top: 0;
}
.modal-dialog.modal-lg {
  width: 90vw;
  max-width: unset!important;
}

.card{
    padding: .5rem;
    margin: .5rem 0;
}
.btn{
    border-radius: .4rem;
}


/*Position cube*/
.position-cube-container{
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(.75);
    padding: 1rem 0;
}
.position-cube {
    width: 100px;
    height: 100px;
    position: relative;
    transform-style: preserve-3d;
    transition: transform .15s;
    transform: rotateX(-45deg) rotateY(45deg);
}
.position-cube-face{
    position: absolute;
    width: 100px;
    height: 100px;
    line-height: 100px;
    font-size: 20px;
    font-weight: bold;
    color: white;
    text-align: center;
    border: 1px solid black;
    background-color: var(--color-card-light);
    opacity: .66;

    user-select: none;
    -moz-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -o-user-select: none;
}
.position-cube-front  { transform: rotateY(  0deg) translateZ(50px); }
.position-cube-right  { transform: rotateY( 90deg) translateZ(50px); }
.position-cube-back   { transform: rotateY(180deg) translateZ(50px); }
.position-cube-left   { transform: rotateY(-90deg) translateZ(50px); }
.position-cube-top    { transform: rotateX( 90deg) translateZ(50px); }
.position-cube-bottom { transform: rotateX(-90deg) translateZ(50px); }

.position-cube-face:hover{
    cursor: pointer;
    opacity: 1;
}


/*Disable*/
.disabled-mode{
    backdrop-filter: grayscale(1) blur(0.06rem);
    margin: 0 -15px;
}
.pointer-event-none{
  pointer-events: none;
  user-select: none;
}

/*Position*/
.left-25-px{
    left: 25px;
}
.left-50-px{
    left: 50px;
}
.left-75-px{
    left: 75px;
}
.left-100-px{
    left: 100px;
}
.left-125-px{
    left: 125px;
}
.left-150-px{
    left: 150px;
}
.left-175-px{
    left: 175px;
}
.left-200-px{
    left: 200px;
}
.left-225-px{
    left: 225px;
}
.left-250-px{
    left: 250px;
}
.left-275-px{
    left: 275px;
}
.left-300-px{
    left: 300px;
}

.left-50 {
    left: 50%;
}
.top-50{
    top: 50%;
}
.bottom-50{
    bottom: 50%;
}
.translate-middle-x {
    transform: translateX(-50%) !important;
}
.translate-middle-y {
    transform: translateY(-50%) !important;
}
.translate-middle {
    transform: translateY(-50%) translateX(-50%) !important;
}
.translate-middle-from-bottom {
    transform: translateY(50%) translateX(-50%) !important;
}

/*Address Models*/
.address-model-preview{
    opacity: .4;
    transition: .1s;
    cursor: pointer;
}
.address-model-preview:hover, .address-model-preview.active{
    opacity: 1;
}
.address-model-preview img{
    border-radius: .25rem;
}

/*Timeline*/
.timeline-container{
    max-width: 90vw;
}


/*Color Picker*/
.color-picker{
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15) !important;
    border: .125rem solid transparent !important;
    background: unset !important;
}
.color-picker *{
    color: white!important;
}
.color-picker input{
    border-radius: 0.5rem!important;
    border: 1px solid var(--color-input-border)!important;
    color: var(--color-input-text)!important;
}
.color-picker .hex-text .box {
    padding: 4px 24px 8px 8px!important;
}
.color-picker .hsla-text,
.color-picker .rgba-text,
.color-picker .hex-text{
    background-color: var(--color-card);
}
.color-picker .hue-alpha.box{
    background-color: var(--color-card);
    margin-bottom: 0!important;
}
.color-picker .type-policy-arrow{
    cursor: pointer;
}

/*Z-Index*/
.z-index-9{
    z-index: 9;
}
.z-index-99{
    z-index: 99;
}
.z-index-999{
    z-index: 999;
}
.z-index-9999{
    z-index: 9999;
}

/*Shadow*/
.shadow-cover{
    box-shadow: 0 0 100rem 100rem rgba(0, 0, 0, 0.66);
}

/*Animations*/
.ease-in-out{
    transition: 0.66s cubic-bezier(0.8, 0.0, 0.2, 1);
}

/*Rotation*/
.rotate-0{
    transform: rotate(0deg);
    transition: transform 0.2s ease-in-out;
}
.rotate-180{
    transform: rotate(180deg);
    transition: transform 0.2s ease-in-out;
}

/*Images*/
.background-center{
  background-size: cover;
  background-position: center;
}

/*Flex*/
.flex-start{
  display: flex;
  align-items: center;
  justify-content: start;
}
.flex-end{
  display: flex;
  align-items: center;
  justify-content: end;
}
.flex-align{
  display: flex;
  align-items: center;
}
.flex-between{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-center{
  display: flex;
  align-items: center;
  justify-content: center;
}

/*Dot Glow*/
.dot-glow{
  display: inline-block;
  width: 10px;
  height: 10px;
  min-width: 10px;
  border-radius: 50rem;
  transition: .3s;
}
.dot-glow-sm{
  width: 7.5px;
  height: 7.5px;
  min-width: 7.5px;
}
.dot-glow-success{
  box-shadow: 0 0 0 0.25rem #19d89533;
  background-color: #19d895;
}
.dot-glow-danger{
  box-shadow: 0 0 0 .25rem #ff625833;
  background-color: #ff6258;
}
.dot-glow-secondary{
  box-shadow: 0 0 0 .25rem #c0c2c333;
  background-color: #c0c2c3;
}
.dot-glow-primary{
  box-shadow: 0 0 0 .25rem #2196f333;
  background-color: #2196f3;
}
.dot-glow-warning{
  box-shadow: 0 0 0 .25rem #ffaf0033;
  background-color: #ffaf00;
}

/*Opacity*/
.opacity-1 {
  opacity: 1!important;
}
.opacity-85 {
  opacity: 0.85!important;
}
.opacity-75 {
  opacity: 0.75!important;
}
.opacity-50 {
  opacity: 0.5!important;
}
.opacity-25 {
  opacity: 0.25!important;
}
.opacity-33 {
  opacity: 0.33!important;
}
.opacity-0 {
  opacity: 0!important;
}

/*Height*/
.vh-25{
  height: 25vh;
}
.vh-33{
  height: 33vh;
}
.vh-50{
  height: 50vh;
}
.vh-66{
  height: 66vh;
}
.vh-75{
  height: 75vh;
}

/*Screen Selection*/
.screen-selector{
  position: absolute;
  pointer-events: none;
  border: 3px solid #fb8300;
  background-color: #FB830059;
  border-radius: .5rem;
}

/*Surface Scan*/
.surface-scan-header{
  margin-left: 125px;
  margin-bottom: .5rem;
  display: flex;
  align-items: start;
  justify-content: center;
  transition: .5s;
  overflow: hidden;
  height: 0px;
  opacity: 0;
}
.surface-scan-header.active{
  height: 25px;
  opacity: 1;
}

.surface-scan-badge{
  background-color: var(--color-card-section);
  border-radius: 1rem;
  font-size: .75rem;
  padding: .25rem .5rem;
  margin-right: .5rem;
  display: flex;
  align-items: center;
}
.surface-scan-canvas{
  height: calc(100% - 120px);
  width: calc(100% - 120px);
  display: block;
  object-fit: contain;
  transition: .3s;
  background-color: var(--color-card-section);
  border-radius: 1rem;
  /*cursor: none;*/
}
.surface-scan-scale{
  background-image: linear-gradient(180deg, #FF0000, #00FF00);
  background-image: linear-gradient(180deg, rgb(235, 45, 39), rgb(242, 121, 30), rgb(249, 253, 56), rgb(85, 199, 104), rgb(36, 150, 209), rgb(78, 61, 145), rgb(48, 18, 59));
  width: 25px;
  border-radius: .5rem;
  height: 100%;
}
.surface-scan-scale-numbers{
  width: 65px;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  font-size: .9rem;
  cursor: n-resize;
}
.surface-scan-scale-number{
  position: absolute;
  left: 0;
  transform: translateY(-50%);
  pointer-events: none;
  user-select: none;
}

.surface-scan-bottom-chart{
  position: absolute;
  width: calc(100% - 120px);
  height: 100px;
  transition: .3s;
  left: 120px;
  bottom: 0;
  background-color: var(--color-card-section);
  border-radius: 1rem;
}
.surface-scan-bottom-chart canvas{
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.surface-scan-side-chart{
  position: absolute;
  width: 100px;
  height: calc(100% - 120px);
  margin-bottom: 120px;
  transition: .3s;
  left: 0;
  top: 0;
  background-color: var(--color-card-section);
  border-radius: 1rem;
}
.surface-scan-side-chart canvas{
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.surface-scan-preview{
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  margin-left: .25rem;
  background-color: var(--color-card-section);
  border-radius: 1rem;
}
.surface-scan-preview-dot{
  width: 10px;
  height: 10px;
  border-radius: 10rem;
  margin-right: .5rem;
  transition: .3s;
}
.surface-scan-numbers-container{
  position: relative;
  margin: 0 .5rem 0 20px;
  height: 100%;
  display: flex;
  align-items: center;
  background-color: var(--color-card-section);
  border-radius: 1rem;
  padding: .5rem;
}

.surface-scan-indicator-center{
  position: absolute;
  width: 16px;
  height: 16px;
  transform: translateY(-50%) translateX(-50%);
  border: 1px solid white;
  border-radius: 10rem;
  backdrop-filter: blur(10px);
}
.surface-scan-indicator-line{
  position: absolute;
  background-color: white;
  border: 1px solid white;
}
.surface-scan-indicator-horizontal{
  transform: translateY(-50%);
}
.surface-scan-indicator-vertical{
  transform: translateX(-50%);
}

.surface-scan-label{
  position: absolute;
  left: 1rem;
  top: 0;
  transform: translateY(-100%);
  background-color: var(--color-card-section);
  padding: .25rem .5rem 0;
  border-top-left-radius: .25rem;
  font-size: .65rem;
  line-height: 0.8;
  border-top-right-radius: .25rem;
  color: #6c757d !important;
  white-space: nowrap;
}
.surface-scan-label-vertical{
  top: .75rem;
  left: 100%;
  transform: translateY(0) rotate(90deg) translateX(0.25rem) translateY(-100%);
  transform-origin: top left;
}

/*Loading Bar*/
.loading-bar-container{
  background-color: rgba(37, 44, 70, 0.2);
  margin-right: .25rem;
  overflow: hidden;
  border-radius: 50rem;
  width: 150px;
}
.loading-bar {
  height: 10px;
  border-radius: 50rem;
  transition: .01s;

  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.15) 25%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    rgba(255, 255, 255, 0.05) 100%
  );
  background-size: 300% 100%;
  animation: shimmer-bar 3s infinite linear;
}

@keyframes shimmer-bar {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/*Popups*/
.popup-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.66);
  transition: .3s;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}
.popup-backdrop.active {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
  z-index: 98;
}

/*Preview popup*/
.preview-popup-container{
  transition: .3s;
  cursor: pointer;
}
.preview-popup-container:hover{
  transform: scale(1.05);
  filter: brightness(1.25);
}
.preview-popup-image{
  user-select: none;
  width: 100%;
  height: 100%;
}
